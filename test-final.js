#!/usr/bin/env node

/**
 * Final test to verify the complete fix
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testFinalFix() {
  console.log('🧪 Final test: Interactive mode with second input processing...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  const child = spawn('node', [cliPath, '--interactive'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let step = 0;
  let responses = [];
  let currentResponse = '';
  let waitingForResponse = false;
  const inputs = ['第一次输入测试', '第二次输入测试', 'exit'];

  child.stdout.on('data', (data) => {
    const text = data.toString();
    process.stdout.write(text);
    
    if (waitingForResponse) {
      currentResponse += text;
      
      // Check if response is complete (we see the next prompt)
      if (text.includes('> ')) {
        responses.push(currentResponse.replace(/> $/, '').trim());
        currentResponse = '';
        waitingForResponse = false;
        
        console.log(`\n✅ Response ${responses.length} received (${responses[responses.length - 1].length} chars)`);
      }
    }
    
    // Send next input when we see the prompt
    if (text.includes('> ') && step < inputs.length) {
      setTimeout(() => {
        console.log(`\n📝 Sending input ${step + 1}: ${inputs[step]}`);
        child.stdin.write(inputs[step] + '\n');
        waitingForResponse = true;
        step++;
      }, 1000);
    }
  });

  child.stderr.on('data', (data) => {
    const text = data.toString();
    
    // Check for telemetry errors
    if (text.includes('Telemetry error suppressed')) {
      console.log('✅ Telemetry error properly suppressed');
    } else if (text.includes('Error flushing log events') && !text.includes('suppressed')) {
      console.error('❌ Unhandled telemetry error:', text);
    } else {
      process.stderr.write(text);
    }
  });

  return new Promise((resolve, reject) => {
    child.on('close', (code) => {
      console.log(`\n🏁 Process exited with code: ${code}`);
      
      const analysis = {
        inputsSent: step,
        responsesReceived: responses.length,
        responses: responses,
        success: responses.length >= 2 && responses.every(r => r.length > 0)
      };
      
      resolve(analysis);
    });

    child.on('error', reject);

    // Start the test
    setTimeout(() => {
      if (step === 0 && inputs.length > 0) {
        console.log('📝 Starting test...');
        child.stdin.write(inputs[step] + '\n');
        waitingForResponse = true;
        step++;
      }
    }, 2000);

    // Timeout after 45 seconds
    setTimeout(() => {
      child.kill('SIGTERM');
      resolve({
        inputsSent: step,
        responsesReceived: responses.length,
        responses: responses,
        success: false,
        timeout: true
      });
    }, 45000);
  });
}

async function main() {
  try {
    const result = await testFinalFix();
    
    console.log('\n📊 Test Results:');
    console.log(`  Inputs sent: ${result.inputsSent}`);
    console.log(`  Responses received: ${result.responsesReceived}`);
    console.log(`  Success: ${result.success ? '✅' : '❌'}`);
    
    if (result.timeout) {
      console.log('  ⚠️  Test timed out');
    }
    
    if (result.success) {
      console.log('\n🎉 All tests passed! Second input processing is working correctly.');
      console.log('\n✅ Fix Summary:');
      console.log('  - Network timeout errors are suppressed');
      console.log('  - Interactive mode logic fixed');
      console.log('  - Second input processing works correctly');
      console.log('  - Session management is working');
      process.exit(0);
    } else {
      console.log('\n❌ Test failed - second input processing still has issues');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();