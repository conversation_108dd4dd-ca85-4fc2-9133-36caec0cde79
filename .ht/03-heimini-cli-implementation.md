# hai-code 入口命令实现方案

## 目标

创建一个新的入口命令 `hai-code`，该命令执行时调用lang包中的agent逻辑，并支持从环境变量读取LLM配置参数。

## 进度记录

### 阶段1：分析现有代码结构 ✅

1. **现有CLI入口分析**：
   - 主入口：`packages/cli/src/gemini.tsx`
   - CLI配置：`packages/cli/src/config/config.ts`
   - 参数解析：使用yargs库解析命令行参数

2. **Lang包结构分析**：
   - 主入口：`packages/lang/src/index.ts`
   - Agent实现：`packages/lang/src/core/agent.ts`
   - CLI集成：`packages/lang/src/cli-integration.ts`
   - 配置管理：`packages/lang/src/config/config.ts`

3. **环境变量支持现状**：
   - 已支持的环境变量：
     - `OPENAI_API_KEY`
     - `OPENAI_BASE_URL`
     - `ANTHROPIC_API_KEY`
     - `GEMINI_API_KEY`
     - `GOOGLE_CLOUD_PROJECT`
   - 配置位置：`packages/lang/src/config/config.ts` 的 `getAuthType()` 和 `initialize()` 方法

### 阶段2：实现计划

#### 2.1 创建hai-code入口脚本
- 位置：`packages/lang/src/cli.ts`
- 功能：命令行参数解析、环境变量读取、调用agent逻辑

#### 2.2 配置package.json
- 在根目录package.json中添加bin入口
- 或在lang包的package.json中添加bin入口

#### 2.3 环境变量配置
- 支持读取：
  - `OPENAI_BASE_URL`
  - `OPENAI_API_KEY`
  - `ANTHROPIC_API_KEY`
  - `GEMINI_API_KEY`
  - `GOOGLE_CLOUD_PROJECT`
  - 其他相关配置

#### 2.4 Agent调用逻辑
- 使用 `createLangChainGeminiCLI` 函数
- 处理用户输入和输出
- 支持流式响应

## 实现步骤

1. ✅ 创建CLI入口脚本
2. ✅ 配置package.json bin入口
3. ✅ 实现参数解析和环境变量读取
4. ✅ 调用lang包agent逻辑
5. ✅ 测试新命令

## 技术细节

### 支持的LLM提供商
- OpenAI兼容接口（通过OPENAI_BASE_URL和OPENAI_API_KEY）
- Anthropic（通过ANTHROPIC_API_KEY）
- Google Gemini（通过GEMINI_API_KEY）
- Google Vertex AI（通过GOOGLE_CLOUD_PROJECT）

### 参数映射
- `--model`: 指定模型名称
- `--prompt`: 非交互式模式的prompt
- `--debug`: 调试模式
- 环境变量自动检测认证类型

## 测试结果 ✅

### 功能验证
1. **命令入口**: `hai-code` 命令成功创建并可执行
2. **帮助系统**: `--help` 和 `--version` 参数正常工作
3. **环境变量检测**: 自动检测并识别LLM提供商
   - ✅ OPENAI_API_KEY 检测
   - ✅ OPENAI_BASE_URL 检测
   - ✅ GOOGLE_CLOUD_PROJECT 检测
   - ✅ 认证类型自动识别（USE_OPENAI_COMPATIBLE）
4. **工具发现**: 成功发现11个核心工具
5. **LangChain集成**: Agent成功启动并处理消息
6. **会话管理**: 会话ID生成和管理正常

### 配置文件更新
- ✅ `packages/lang/package.json`: 添加了 `"hai-code": "dist/cli.js"` bin入口
- ✅ `package.json`: 添加了 `"hai-code": "packages/lang/dist/cli.js"` bin入口

## 使用方法

### 安装和构建
```bash
# 在项目根目录
cd packages/lang
npm run build
```

### 基本使用
```bash
# 显示帮助
node dist/cli.js --help

# 显示版本
node dist/cli.js --version

# 非交互式模式
node dist/cli.js "What is the capital of France?"

# 使用管道输入
echo "Explain quantum computing" | node dist/cli.js

# 调试模式
node dist/cli.js --debug "hello world"

# 指定模型
node dist/cli.js -m gpt-3.5-turbo "Explain AI"

# 交互模式
node dist/cli.js --interactive

cd "/Users/<USER>/projs/github/gemini-cli" && echo "介绍这个仓库" | node packages/lang/dist/cli.js --model ht::saas-deepseek-r1
```

#### 交互模式

```sh
# 启动交互模式
$ node packages/lang/dist/cli.js --interactive

🤖 hai-code - Interactive mode
Type "exit" or "quit" to exit, Ctrl+C to quit immediately

> 第一次输入测试                    # 输入第一个问题
[CLI] Received input: "第一次输入测试"
[LangChainAgent] Streaming message: 第一次输入测试
CLI agent responsiveness... ✔️  
Ready for software engineering tasks.

> 第二次输入测试                    # 等待提示符出现后输入第二个问题
[CLI] Received input: "第二次输入测试"
[LangChainAgent] Streaming message: 第二次输入测试
**就绪**  
请提供具体任务或需要解决的问题。

> exit                             # 退出
Goodbye!
```

### 环境变量配置

#### OpenAI兼容接口
```bash
export OPENAI_API_KEY="your_api_key"
export OPENAI_BASE_URL="http://localhost:11434/v1"  # 可选，用于自托管服务
node dist/cli.js "Hello"
```

#### Google Gemini
```bash
export GEMINI_API_KEY="your_gemini_api_key"
node dist/cli.js "Hello"
```

#### Anthropic Claude
```bash
export ANTHROPIC_API_KEY="your_anthropic_api_key"
node dist/cli.js "Hello"
```

#### Google Vertex AI
```bash
export GOOGLE_GENAI_USE_VERTEXAI="true"
export GOOGLE_CLOUD_PROJECT="your_project_id"
# 需要先认证: gcloud auth application-default login
node dist/cli.js "Hello"
```

### 高级使用
```bash
# 使用Ollama本地模型
OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama node dist/cli.js -m llama3 "Hello"

# 自定义模型名称
HAI_CODE_MODEL=gpt-4o-mini node dist/cli.js "Hello"
```

## 特性
- ✅ 支持多种LLM提供商（OpenAI、Anthropic、Google）
- ✅ 自动环境变量检测和认证类型识别
- ✅ 交互式和非交互式模式
- ✅ 流式响应支持
- ✅ 调试模式
- ✅ 管道输入支持
- ✅ 工具发现和集成
- ✅ 会话管理

## 实施完成状态
🎉 **所有功能已成功实现并测试通过！**