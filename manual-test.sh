#!/bin/bash

echo "Starting manual interactive test..."

# Create a temporary script to send inputs
cat > /tmp/test_inputs.txt << 'EOF'
第一次输入测试
第二次输入测试
exit
EOF

echo "Inputs prepared:"
cat /tmp/test_inputs.txt

echo -e "\nStarting CLI with inputs..."

# Run the CLI with inputs
node packages/lang/dist/cli.js --interactive --debug < /tmp/test_inputs.txt

echo -e "\nTest completed"
rm -f /tmp/test_inputs.txt