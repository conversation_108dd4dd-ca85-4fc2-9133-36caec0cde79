#!/usr/bin/env node

/**
 * Test the LangChain agent directly
 */

import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';

console.log('Testing simplified agent logic...');

const model = new ChatOpenAI({
  modelName: 'ht::saas-deepseek-r1',
  apiKey: process.env.OPENAI_API_KEY || 'test-key',
  temperature: 0.7,
  maxTokens: 1000,
  streaming: false,
  configuration: {
    baseURL: process.env.OPENAI_BASE_URL || 'http://168.63.85.222/web/unauth/LLM_api_proxy/v1',
  },
});

async function testAgent() {
  try {
    console.log('Creating messages...');
    
    const messages = [
      new SystemMessage('You are a helpful assistant.'),
      new HumanMessage('hello world')
    ];
    
    console.log('Invoking model...');
    const response = await model.invoke(messages);
    
    console.log('✅ Success!');
    console.log('Response:', response.content);
    
    // Test the response structure
    if (response && typeof response.content === 'string') {
      console.log('✅ Response structure is correct');
      return response.content;
    } else {
      console.log('❌ Unexpected response structure');
      console.log('Response type:', typeof response);
      console.log('Content type:', typeof response?.content);
      return 'No response generated';
    }
    
  } catch (error) {
    console.error('❌ Error in agent test:', error.message);
    
    // Check for specific error patterns
    if (error.message.includes('Cannot read properties of undefined')) {
      console.error('This is the malformed response error we were seeing');
    }
    
    throw error;
  }
}

testAgent();