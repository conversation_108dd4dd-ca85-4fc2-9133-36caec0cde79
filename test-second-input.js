#!/usr/bin/env node

/**
 * Test to verify second input processing
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testSecondInput() {
  console.log('🧪 Testing second input processing...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  const child = spawn('node', [cliPath, '--interactive'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let step = 0;
  let receivedFirstResponse = false;
  let receivedSecondResponse = false;
  const inputs = ['第一次输入测试', '第二次输入测试', 'exit'];

  child.stdout.on('data', (data) => {
    const text = data.toString();
    console.log('STDOUT:', text);
    
    // Check for responses
    if (text.includes('第一次') || text.includes('测试') || text.includes('功能')) {
      if (!receivedFirstResponse) {
        receivedFirstResponse = true;
        console.log('✅ First response received');
      } else if (!receivedSecondResponse) {
        receivedSecondResponse = true;
        console.log('✅ Second response received');
      }
    }
    
    // Send next input when we see the prompt
    if (text.includes('> ') && step < inputs.length) {
      setTimeout(() => {
        console.log(`\n📝 Sending input ${step + 1}: ${inputs[step]}`);
        child.stdin.write(inputs[step] + '\n');
        step++;
      }, 1000);
    }
  });

  child.stderr.on('data', (data) => {
    const text = data.toString();
    console.log('STDERR:', text);
  });

  return new Promise((resolve, reject) => {
    child.on('close', (code) => {
      console.log(`\n🏁 Process exited with code: ${code}`);
      
      if (receivedFirstResponse && receivedSecondResponse) {
        console.log('✅ Both inputs were processed successfully');
        resolve({ success: true });
      } else if (receivedFirstResponse && !receivedSecondResponse) {
        console.log('❌ Second input was not processed');
        reject(new Error('Second input not processed'));
      } else {
        console.log('❌ No responses received');
        reject(new Error('No responses received'));
      }
    });

    child.on('error', reject);

    // Start the test
    setTimeout(() => {
      if (step === 0) {
        console.log('📝 Starting test...');
        child.stdin.write(inputs[step] + '\n');
        step++;
      }
    }, 2000);

    // Timeout after 30 seconds
    setTimeout(() => {
      child.kill('SIGTERM');
      if (receivedFirstResponse && receivedSecondResponse) {
        resolve({ success: true });
      } else {
        reject(new Error('Test timeout - second input may not have been processed'));
      }
    }, 30000);
  });
}

async function main() {
  try {
    await testSecondInput();
    console.log('\n🎉 Second input processing test passed!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();