#!/usr/bin/env node

/**
 * Test script to verify API endpoint compatibility
 */

import fetch from 'node-fetch';

const API_BASE = process.env.OPENAI_BASE_URL || 'http://*************/web/unauth/LLM_api_proxy/v1';
const API_KEY = process.env.OPENAI_API_KEY || 'test-key';

console.log('Testing API endpoint compatibility...');
console.log('API Base URL:', API_BASE);
console.log('API Key:', API_KEY ? 'set' : 'not set');

async function testModels() {
  console.log('\n--- Testing /models endpoint ---');
  try {
    const response = await fetch(`${API_BASE}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
      },
    });

    console.log('Models endpoint status:', response.status);
    const data = await response.text();
    console.log('Models response:', data);
    
    if (response.ok) {
      try {
        const json = JSON.parse(data);
        if (json.data && Array.isArray(json.data)) {
          console.log('Available models:', json.data.map(m => m.id || m.name || m));
        }
      } catch (e) {
        console.log('Could not parse models response as JSON');
      }
    }
  } catch (error) {
    console.error('Error fetching models:', error.message);
  }
}

async function testAPI() {
  console.log('\n--- Testing chat completions ---');
  
  // Try different model names from the available list
  const modelsToTry = [
    'ht::saas-deepseek-r1',
    'ht::local-deepseek-r1', 
    'ht::saas-doubao-15-pro-32k',
    'ht::saas-deepseek-v3',
    'openrouter::anthropic/claude-3.5-sonnet',
    'openrouter::google/gemini-2.5-pro-preview'
  ];
  
  for (const model of modelsToTry) {
    console.log(`\nTrying model: ${model}`);
    
    try {
      const response = await fetch(`${API_BASE}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`,
        },
        body: JSON.stringify({
          model: model,
          messages: [
            { role: 'user', content: 'Hello, world!' }
          ],
          max_tokens: 100,
          temperature: 0.7,
        }),
      });

      console.log('  Status:', response.status);
      const data = await response.text();
      console.log('  Response:', data.substring(0, 200) + (data.length > 200 ? '...' : ''));

      if (response.ok) {
        try {
          const json = JSON.parse(data);
          if (json.choices && json.choices[0] && json.choices[0].message) {
            console.log('  ✅ SUCCESS! Model works with OpenAI format');
            console.log('  Message:', json.choices[0].message.content);
            return; // Found a working model, exit
          } else {
            console.log('  ❌ Response not in OpenAI format');
          }
        } catch (parseError) {
          console.log('  ❌ Response not valid JSON');
        }
      } else {
        console.log('  ❌ Request failed');
      }
    } catch (error) {
      console.error('  ❌ Network error:', error.message);
    }
  }
}

async function main() {
  await testModels();
  await testAPI();
}

main();