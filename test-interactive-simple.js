#!/usr/bin/env node

/**
 * Simple interactive test to verify the fix
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testInteractive() {
  console.log('🧪 Testing interactive mode with network timeout fix...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  const child = spawn('node', [cliPath, '--interactive'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let hasUnhandledError = false;
  let step = 0;
  const inputs = ['介绍这个仓库', '分析packages目录', 'exit'];

  child.stdout.on('data', (data) => {
    const text = data.toString();
    process.stdout.write(text);
    
    // Send next input when we see the prompt
    if (text.includes('> ') && step < inputs.length) {
      setTimeout(() => {
        console.log(`\n📝 Sending: ${inputs[step]}`);
        child.stdin.write(inputs[step] + '\n');
        step++;
      }, 1000);
    }
  });

  child.stderr.on('data', (data) => {
    const text = data.toString();
    
    // Check for unhandled network errors
    if (text.includes('Error flushing log events') && !text.includes('suppressed')) {
      console.error('❌ Unhandled telemetry error:', text);
      hasUnhandledError = true;
    } else if (text.includes('Telemetry error suppressed')) {
      console.log('✅ Telemetry error properly suppressed');
    } else {
      process.stderr.write(text);
    }
  });

  return new Promise((resolve, reject) => {
    child.on('close', (code) => {
      if (hasUnhandledError) {
        reject(new Error('Unhandled telemetry errors detected'));
      } else {
        console.log('\n✅ Test completed successfully - no unhandled telemetry errors');
        resolve(code);
      }
    });

    child.on('error', reject);

    // Start the test
    setTimeout(() => {
      if (step === 0) {
        console.log('📝 Starting test...');
        child.stdin.write(inputs[step] + '\n');
        step++;
      }
    }, 2000);

    // Timeout after 30 seconds
    setTimeout(() => {
      child.kill('SIGTERM');
      if (!hasUnhandledError) {
        console.log('\n✅ Test timeout reached - no unhandled errors detected');
        resolve(0);
      }
    }, 30000);
  });
}

async function main() {
  try {
    await testInteractive();
    console.log('\n🎉 Network timeout fix test passed!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();