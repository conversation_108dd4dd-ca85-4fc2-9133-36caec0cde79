# @ht/hai-code-cli

基于 LangChain.js 和 LangGraph 的 Gemini CLI 核心功能实现。

## 概述

这个包提供了与 `@google/gemini-cli-core` 同等功能的替代实现，使用 LangChain.js 和 LangGraph 作为底层框架。主要特点：

- **LangChain.js 集成**: 使用 LangChain 的聊天模型、工具和嵌入功能
- **LangGraph 状态管理**: 使用 LangGraph 实现复杂的代理执行流程
- **工具兼容性**: 复用 core 包的工具定义，确保功能一致性
- **会话管理**: 完整的会话历史和记忆管理
- **类型安全**: 完整的 TypeScript 支持

## 安装

```bash
npm install @ht/hai-code-cli
```

## 快速开始

```typescript
import { createLangChainGeminiCLI } from '@ht/hai-code-cli';
import type { ConfigParameters } from '@google/gemini-cli-core';

const params: ConfigParameters = {
  sessionId: 'my-session',
  targetDir: '/path/to/project',
  debugMode: false,
  model: 'gemini-1.5-flash',
  cwd: process.cwd(),
};

const cli = await createLangChainGeminiCLI(params);

// 处理消息
const response = await cli.processMessage('Hello, how can you help me?');
console.log(response);

// 流式处理
for await (const chunk of cli.streamMessage('Explain this code:')) {
  process.stdout.write(chunk);
}
```

## 主要组件

### LangChainConfig

配置管理器，管理认证、模型和工具设置：

```typescript
import { LangChainConfig } from '@ht/hai-code-cli';

const config = new LangChainConfig(params);
await config.initialize();

// 获取 LangChain 配置
const langChainConfig = config.getLangChainConfig();
```

### LangChainAgent

基于 LangGraph 的代理，处理消息和工具执行：

```typescript
import { LangChainAgent } from '@ht/hai-code-cli';

const agent = new LangChainAgent(config.getLangChainConfig());

// 处理消息
const response = await agent.processMessage('Hello', 'session-id');

// 流式处理
for await (const chunk of agent.streamMessage('Hello', 'session-id')) {
  console.log(chunk);
}
```

### SessionManager

会话管理器，处理对话历史和记忆：

```typescript
import { SessionManager } from '@ht/hai-code-cli';

const sessionManager = new SessionManager();

// 创建会话
const sessionId = sessionManager.createSession('User memory');

// 获取会话
const session = sessionManager.getSession(sessionId);

// 更新会话
sessionManager.updateSession(sessionId, messages, 'Updated memory');
```

### LangChainContentGenerator

内容生成器，包装 LangChain 模型：

```typescript
import { LangChainContentGenerator } from '@ht/hai-code-cli';

const generator = new LangChainContentGenerator(chatModel, embeddings, tools);

// 生成内容
const response = await generator.generateContent(request);

// 流式生成
for await (const chunk of generator.generateContentStream(request)) {
  console.log(chunk);
}
```

## 工具系统

### CoreToolWrapper

将 core 包的工具适配到 LangChain 工具系统：

```typescript
import { CoreToolWrapper } from '@ht/hai-code-cli';

// 创建工具包装器
const wrapper = new CoreToolWrapper(coreTool, config);

// 在 LangChain 中使用
const modelWithTools = chatModel.bindTools([wrapper]);
```

### LangChainToolRegistry

工具注册表，管理所有可用工具：

```typescript
import { LangChainToolRegistry } from '@ht/hai-code-cli';

const registry = new LangChainToolRegistry(config);

// 注册工具
registry.registerCoreTool(coreTool);

// 获取 LangChain 工具
const tools = registry.getLangChainTools();
```

## 模型工厂

### createChatModel

根据认证类型创建适当的聊天模型：

```typescript
import { createChatModel, AuthType } from '@ht/hai-code-cli';

const chatModel = await createChatModel('ht::saas-deepseek-v3', AuthType.USE_OPENAI_COMPATIBLE);
```

### createEmbeddings

创建嵌入模型：

```typescript
import { createEmbeddings } from '@ht/hai-code-cli';

const embeddings = await createEmbeddings('ht::bge-embedding', AuthType.USE_OPENAI_COMPATIBLE);
```

## 认证类型

支持与 core 包相同的认证类型：

- `AuthType.USE_OPENAI_COMPATIBLE`: OpenAI 兼容模式(+)
- `AuthType.LOGIN_WITH_GOOGLE`: OAuth 个人认证
- `AuthType.USE_GEMINI`: Gemini API 密钥
- `AuthType.USE_VERTEX_AI`: Vertex AI
- `AuthType.CLOUD_SHELL`: Cloud Shell

## 与 Core 包的兼容性

这个包设计为与 core 包完全兼容：

- 相同的接口和类型定义
- 相同的工具行为
- 相同的配置参数
- 相同的认证流程

主要区别在于底层实现使用 LangChain.js 而不是直接的 API 调用。

## 开发

### 安装依赖

```bash
npm install
```

### 构建

```bash
npm run build
```

### 测试

```bash
npm test
```

### 代码检查

```bash
npm run lint
```

## 架构

```
src/
├── config/           # 配置管理
│   └── config.ts
├── core/            # 核心组件
│   ├── agent.ts     # LangGraph 代理
│   ├── contentGenerator.ts  # 内容生成器
│   ├── modelFactory.ts      # 模型工厂
│   └── sessionManager.ts    # 会话管理
├── tools/           # 工具系统
│   ├── toolRegistry.ts      # 工具注册表
│   └── coreTools.ts        # 核心工具适配
├── types/           # 类型定义
│   └── index.ts
└── index.ts         # 主入口
```
