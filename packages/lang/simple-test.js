#!/usr/bin/env node

/**
 * Simple test to verify tool calling functionality
 */

import { createLangChainGeminiCLI } from './dist/index.js';

async function simpleTest() {
  console.log('🧪 简单工具调用测试...\n');

  try {
    // Create CLI instance
    const cli = await createLangChainGeminiCLI({
      sessionId: 'simple-test',
      model: 'ht::saas-deepseek-v3',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
      coreTools: ['list_directory'], // Only enable one tool for testing
    });

    console.log('✅ CLI实例创建成功');
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    console.log(`📋 可用工具: ${cli.config.getTools().map(t => t.name).join(', ')}\n`);

    // Check if tools are properly bound
    const configInfo = cli.agent.getConfigInfo();
    console.log(`📊 Agent配置信息:`);
    console.log(`   - 模型: ${configInfo.modelName}`);
    console.log(`   - 工具数量: ${configInfo.toolCount}`);
    console.log(`   - 有系统提示: ${configInfo.hasSystemPrompt}`);

    if (configInfo.toolCount > 0) {
      console.log('\n✅ 工具调用逻辑修复成功！');
      console.log('   - 工具已正确注册');
      console.log('   - Agent可以访问工具');
      console.log('   - 系统提示包含工具信息');
    } else {
      console.log('\n❌ 工具调用逻辑仍有问题');
      console.log('   - 没有检测到可用工具');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// Check environment
function checkEnvironment() {
  const hasOpenAI = process.env.OPENAI_API_KEY && process.env.OPENAI_BASE_URL;
  const hasAnthropic = process.env.ANTHROPIC_API_KEY;
  const hasGemini = process.env.GEMINI_API_KEY;

  if (!hasOpenAI && !hasAnthropic && !hasGemini) {
    console.log('⚠️  警告: 没有检测到API密钥环境变量');
    console.log('这个测试只验证工具注册，不会实际调用LLM\n');
  }

  return true; // Continue test even without API keys
}

async function main() {
  console.log('🚀 Lang包工具调用修复验证\n');
  
  checkEnvironment();
  await simpleTest();
}

main().catch(console.error);
