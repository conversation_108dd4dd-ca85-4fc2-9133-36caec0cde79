/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * OpenAI Compatible支持演示
 * 
 * 这个示例展示了如何使用Lang包支持不同的AI模型提供商
 */

import { createLangChainGeminiCLI } from '../dist/index.js';

async function demoOpenAICompatible() {
  console.log('🤖 Lang包 OpenAI Compatible 支持演示\n');

  // 示例1: 使用OpenAI API
  console.log('📝 示例1: 使用OpenAI API');
  try {
    // 设置OpenAI API密钥
    process.env.OPENAI_API_KEY = 'your-openai-api-key'; // 请替换为实际的API密钥
    
    const cli = await createLangChainGeminiCLI({
      sessionId: 'openai-demo',
      model: 'gpt-4o-mini',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
    } as any);

    console.log('✅ OpenAI CLI实例创建成功');
    console.log(`📊 模型信息: ${cli.config.getModel()}`);
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    
    // 测试对话
    const response = await cli.processMessage("你好！请简单介绍一下你自己。");
    console.log(`💬 AI回复: ${response.substring(0, 100)}...\n`);
    
  } catch (error) {
    console.log('❌ OpenAI示例失败 (这是预期的，因为没有真实的API密钥)');
    console.log(`   错误: ${error instanceof Error ? error.message : String(error)}\n`);
  }

  // 示例2: 使用Ollama本地模型
  console.log('📝 示例2: 使用Ollama本地模型');
  try {
    // 设置Ollama endpoint
    process.env.OPENAI_BASE_URL = 'http://localhost:11434/v1';
    process.env.OPENAI_API_KEY = 'not-needed-for-ollama';
    
    const cli = await createLangChainGeminiCLI({
      sessionId: 'ollama-demo',
      model: 'llama2',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
    } as any);

    console.log('✅ Ollama CLI实例创建成功');
    console.log(`📊 模型信息: ${cli.config.getModel()}`);
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    
    // 测试流式对话
    console.log('💬 开始流式对话...');
    for await (const chunk of cli.streamMessage("请用一句话介绍你自己。")) {
      process.stdout.write(chunk);
    }
    console.log('\n');
    
  } catch (error) {
    console.log('❌ Ollama示例失败 (这是预期的，因为Ollama服务可能没有运行)');
    console.log(`   错误: ${error instanceof Error ? error.message : String(error)}\n`);
  }

  // 示例3: 使用Anthropic Claude
  console.log('📝 示例3: 使用Anthropic Claude');
  try {
    // 设置Anthropic API密钥
    process.env.ANTHROPIC_API_KEY = 'your-anthropic-api-key'; // 请替换为实际的API密钥
    
    const cli = await createLangChainGeminiCLI({
      sessionId: 'anthropic-demo',
      model: 'claude-3-5-sonnet-20241022',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
    } as any);

    console.log('✅ Anthropic CLI实例创建成功');
    console.log(`📊 模型信息: ${cli.config.getModel()}`);
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    
    // 测试工具使用
    const response = await cli.processMessage("请列出当前目录的文件。");
    console.log(`💬 AI回复: ${response.substring(0, 100)}...\n`);
    
  } catch (error) {
    console.log('❌ Anthropic示例失败 (这是预期的，因为没有真实的API密钥)');
    console.log(`   错误: ${error instanceof Error ? error.message : String(error)}\n`);
  }

  // 示例4: 自动检测认证类型
  console.log('📝 示例4: 自动检测认证类型');
  try {
    // 不设置任何环境变量，让系统自动检测
    const cli = await createLangChainGeminiCLI({
      sessionId: 'auto-detect-demo',
      model: 'gemini-1.5-flash', // 默认使用Gemini
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
    });

    console.log('✅ 自动检测CLI实例创建成功');
    console.log(`📊 模型信息: ${cli.config.getModel()}`);
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    console.log(`🔐 认证类型: 自动检测\n`);
    
  } catch (error) {
    console.log('❌ 自动检测示例失败');
    console.log(`   错误: ${error instanceof Error ? error.message : String(error)}\n`);
  }

  console.log('🎉 演示完成！');
  console.log('\n📋 使用说明:');
  console.log('1. 要使用OpenAI API，设置 OPENAI_API_KEY 环境变量');
  console.log('2. 要使用Ollama，启动Ollama服务并设置 OPENAI_BASE_URL');
  console.log('3. 要使用Anthropic，设置 ANTHROPIC_API_KEY 环境变量');
  console.log('4. 要使用Gemini，设置 GEMINI_API_KEY 环境变量');
  console.log('\n🔗 更多信息请查看 .ht/openai-compatible-usage.md');
}

// 运行演示
demoOpenAICompatible().catch(console.error); 