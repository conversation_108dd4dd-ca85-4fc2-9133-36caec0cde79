/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import logger from '../utils/logger.js';

// Intercept and block telemetry network requests
const originalFetch = globalThis.fetch;
if (originalFetch) {
  globalThis.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
    
    // Block known telemetry endpoints
    if (url.includes('langsmith') || 
        url.includes('langchain') || 
        url.includes('216.239.') ||
        url.includes('googleapis.com') ||
        url.includes('google.com/analytics')) {
      logger.debug('Blocked telemetry request to:', url);
      // Return a fake successful response
      return new Response('{"status": "disabled"}', { 
        status: 200, 
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return originalFetch(input, init);
  };
}

// Override console.error to filter out telemetry errors
// const originalConsoleError = console.error;
// console.error = function(...args: unknown[]) {
//   const message = args.join(' ');
  
//   // Filter out telemetry-related errors
//   if (message.includes('Error flushing log events') ||
//       message.includes('216.239.') ||
//       message.includes('ETIMEDOUT') ||
//       message.includes('langsmith') ||
//       message.includes('tracing')) {
//     logger.debug('Telemetry error suppressed:', message);
//     return;
//   }
  
//   return originalConsoleError.apply(console, args);
// };

// Note: The above code is commented out because we now use the logger module directly
// instead of overriding console.error. This ensures consistent logging throughout the application.
