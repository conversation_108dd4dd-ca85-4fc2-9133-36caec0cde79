import { ExtendedAuthType } from "../types/index.js";

export function determineAuthType(): ExtendedAuthType {
  if (process.env.OPENAI_API_KEY || process.env.OPENAI_BASE_URL) {
    return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
  }
  if (process.env.ANTHROPIC_API_KEY) {
    return ExtendedAuthType.USE_ANTHROPIC;
  }
  if (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true' && process.env.GOOGLE_CLOUD_PROJECT) {
    return ExtendedAuthType.USE_VERTEX_AI;
  }
  if (process.env.GEMINI_API_KEY) {
    return ExtendedAuthType.USE_GEMINI;
  }

  // Default to OpenAI compatible if no specific env vars are set
  return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
}
