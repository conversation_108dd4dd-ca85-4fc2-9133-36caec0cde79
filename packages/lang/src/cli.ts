#!/usr/bin/env node
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * hai-code - A simple CLI entry point that uses the lang package agent logic
 * Supports reading LLM configuration from environment variables like OPENAI_BASE_URL, OPENAI_API_KEY, etc.
 */

// CRITICAL: Set telemetry environment variables BEFORE importing any LangChain modules
process.env.LANGCHAIN_TRACING_V2 = 'false';
process.env.LANGCHAIN_TRACING = 'false';
process.env.LANGCHAIN_ENDPOINT = '';
process.env.LANGCHAIN_API_KEY = '';
process.env.LANGSMITH_API_KEY = '';
process.env.LANGSMITH_TRACING = 'false';
process.env.OPENAI_ORGANIZATION = '';
process.env.LANGCHAIN_CALLBACKS_BACKGROUND = 'false';
process.env.LANGCHAIN_VERBOSE = 'false';
process.env.LANGCHAIN_DEBUG = 'false';
process.env.LANGCHAIN_LOG_LEVEL = 'ERROR';
process.env.LANGCHAIN_DISABLE_TELEMETRY = 'true';

import './utils-cli/overwrite.js';
import chalk from 'chalk';
import logger from './utils/logger.js';
import { createLangChainGeminiCLI } from './index.js';
import { readStdin } from './utils/readStdin.js';
import { parseCliArguments } from './utils-cli/parseArg.js';
import { determineAuthType } from './utils-cli/authType.js';

// 使用统一的日志模块
const log = logger;

function printHelp() {
  log.info(chalk.cyan(`
hai-code - AI Coding Assistant CLI

Usage:
  hai-code [options] [prompt]
  echo "your question" | hai-code

Options:
  -m, --model <model>     Model to use (default: ht::saas-deepseek-v3)
  -p, --prompt <prompt>   Prompt to process (non-interactive mode)
  -b, --base-url <url>    Base URL for LLM API (can also use OPENAI_BASE_URL env)
  -i, --interactive       Start in interactive mode (default if no prompt)
  -d, --debug             Enable debug mode
  -h, --help              Show this help message
  -v, --version           Show version

Environment Variables:
  OPENAI_API_KEY          OpenAI API key
  OPENAI_BASE_URL         OpenAI-compatible API base URL
  ANTHROPIC_API_KEY       Anthropic API key  
  GEMINI_API_KEY          Google Gemini API key
  GOOGLE_CLOUD_PROJECT    Google Cloud project for Vertex AI

Examples:
  hai-code "法国首都是什么？"
  hai-code -m ht::saas-deepseek-v3 "解释量子计算"
  echo "修复这个代码" | hai-code
  OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama hai-code -m ht::saas-deepseek-v3 "你好"
`));
}

function printVersion() {
  log.info(`hai-code version 0.1.0`);
}

async function runInteractiveMode(cli: {
  streamMessage: (userMessage: string, sessionId?: string, userMemory?: string) => AsyncGenerator<string>;
}) {
  log.success('🤖 hai-code - Interactive mode');
  log.info('Type "exit" or "quit" to exit, Ctrl+C to quit immediately');
  // log.info('');

  // Create a persistent session for the entire interactive session
  const interactiveSessionId = `interactive-${Date.now()}`;
  log.debug(`\n[CLI] Created interactive session: ${interactiveSessionId}`);

  // State for handling permission requests
  let waitingForPermission = false;

  // Use readline for interactive input
  const readline = await import('node:readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '> ',
  });

  rl.prompt();

  rl.on('line', async (input) => {
    const trimmed = input.trim();
    log.debug(`\n[CLI] Received input: "${trimmed}"`);

    if (trimmed === 'exit' || trimmed === 'quit') {
      rl.close();
      return;
    }

    if (trimmed === '') {
      rl.prompt();
      return;
    }

    try {
      // Check if this looks like a permission response
      if (waitingForPermission && isPermissionResponse(trimmed)) {
        log.info(`\nPermission response received: ${trimmed}`);
        // Handle permission response
        const permissionGranted = parsePermissionResponse(trimmed);
        if (permissionGranted) {
          log.success('✅ Permission granted, proceeding...');
        } else {
          log.warning('❌ Permission denied, operation cancelled.');
        }
        waitingForPermission = false;
        rl.prompt();
        return;
      }

      log.debug(`\n[CLI] Starting to stream message for session: ${interactiveSessionId}`);
      
      // Use the same session ID for all messages in this interactive session
      const stream = cli.streamMessage(trimmed, interactiveSessionId);

      let responseContent = '';
      let chunkCount = 0;
      
      try {
        for await (const chunk of stream) {
          chunkCount++;
          responseContent += chunk;
          process.stdout.write(chunk);
          
          // Add periodic debug info for long responses
          if (chunkCount % 50 === 0) {
            log.debug(`\n[CLI] Processed ${chunkCount} chunks, ${responseContent.length} chars`);
          }
        }
        
        log.debug(`\n[CLI] Stream completed: ${chunkCount} chunks, ${responseContent.length} chars`);
      } catch (streamError) {
        log.error('\n[CLI] Stream error:', streamError);
        throw streamError;
      }

      // Check if the response contains a permission request
      if (containsPermissionRequest(responseContent)) {
        waitingForPermission = true;
        log.info('\n💡 Tip: Respond with "y/yes" to approve, "n/no" to deny, or "a/always" to always approve this type of operation.');
      }

      log.info(''); // New line after response
    } catch (error) {
      log.error('\nError:', error instanceof Error ? error.message : String(error));
      log.error('\nError stack:', error instanceof Error ? error.stack : 'No stack trace');
      waitingForPermission = false;
    }

    log.debug('\n[CLI] Prompting for next input');
    rl.prompt();
  });

  rl.on('close', () => {
    log.success('\nGoodbye!');
    process.exit(0);
  });
}

/**
 * Check if input looks like a permission response
 */
function isPermissionResponse(input: string): boolean {
  const normalized = input.toLowerCase();
  const permissionResponses = [
    'y', 'yes', 'n', 'no', 'a', 'always', 'never', 
    'approve', 'deny', 'allow', 'reject', 'proceed', 'cancel'
  ];
  return permissionResponses.includes(normalized);
}

/**
 * Parse permission response to boolean
 */
function parsePermissionResponse(input: string): boolean {
  const normalized = input.toLowerCase();
  const positiveResponses = ['y', 'yes', 'a', 'always', 'approve', 'allow', 'proceed'];
  return positiveResponses.includes(normalized);
}

/**
 * Check if response contains a permission request
 */
function containsPermissionRequest(content: string): boolean {
  const permissionIndicators = [
    'Do you want to proceed',
    'Would you like to continue',
    'Confirm this action',
    'Are you sure',
    'Permission required',
    'Approve this operation',
    'Execute this command',
    'Run this tool',
    'y/n',
    '(y/N)',
    '(Y/n)',
    'yes/no'
  ];
  
  return permissionIndicators.some(indicator => 
    content.toLowerCase().includes(indicator.toLowerCase())
  );
}

async function main() {
  try {
    const { options, prompt } = await parseCliArguments();

    if (options.help) {
      printHelp();
      process.exit(0);
    }

    if (options.version) {
      printVersion();
      process.exit(0);
    }

    if (options.debug) {
      log.info('Debug mode enabled');
      log.info('Environment variables:');
      log.info('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? chalk.green('√') : chalk.red('×'));
      log.info('  OPENAI_BASE_URL:', process.env.OPENAI_BASE_URL ? chalk.green(process.env.OPENAI_BASE_URL) : chalk.red('×'));
      log.info('  ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? chalk.green('√') : chalk.red('×'));
      log.info('  GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? chalk.green('√') : chalk.red('×'));
      log.info('  GOOGLE_CLOUD_PROJECT:', process.env.GOOGLE_CLOUD_PROJECT ? chalk.green(process.env.GOOGLE_CLOUD_PROJECT) : chalk.red('×'));
    }
    log.info('Auth type:', chalk.green(determineAuthType()));
    log.info('Model:', chalk.green(options.model || 'ht::saas-deepseek-r1'));
    log.info('');

    // Create the CLI instance
    const cli = await createLangChainGeminiCLI({
      sessionId: `hai-code-${Date.now()}`,
      model: options.model || 'ht::saas-deepseek-r1',
      targetDir: process.cwd(),
      debugMode: options.debug || false,
      question: '',
      fullContext: false,
      userMemory: '',
      geminiMdFileCount: 50,
      cwd: process.cwd(),
      baseURL: options.baseUrl,
      authType: determineAuthType(),
      telemetry: {
        enabled: false,
      },
    });

    // Determine mode based on explicit flags and input availability
    if (options.interactive) {
      // Explicitly requested interactive mode
      await runInteractiveMode(cli);
    } else if (prompt) {
      // Explicit prompt provided via command line
      try {
        const stream = cli.streamMessage(prompt);
        for await (const chunk of stream) {
          process.stdout.write(chunk);
        }
        log.info(''); // Add final newline
      } catch (error) {
        log.error('Error:', error instanceof Error ? error.message : String(error));
        process.exit(1);
      }
    } else if (!process.stdin.isTTY) {
      // Input from stdin (pipe)
      const stdinInput = await readStdin();
      if (stdinInput.trim()) {
        try {
          const stream = cli.streamMessage(stdinInput.trim());
          for await (const chunk of stream) {
            process.stdout.write(chunk);
          }
          log.info(''); // Add final newline
        } catch (error) {
          log.error('Error:', error instanceof Error ? error.message : String(error));
          process.exit(1);
        }
      } else {
        // Empty stdin, default to interactive
        await runInteractiveMode(cli);
      }
    } else {
      // No explicit prompt and TTY available, default to interactive
      await runInteractiveMode(cli);
    }

  } catch (error) {
    log.error('Fatal error:', error instanceof Error ? error.message : String(error));
    if (error instanceof Error && error.stack) {
      log.error('Stack:', error.stack);
    }
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  // Check if it's a network timeout error that we can ignore
  if (reason && typeof reason === 'object' && 'code' in reason) {
    const error = reason as { code?: string; message?: string };
    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNREFUSED') {
      log.debug('Network timeout ignored (likely telemetry):', error.message);
      return; // Don't exit for network timeouts
    }
  }
  
  // Check for LangChain telemetry errors
  if (reason && typeof reason === 'object' && 'message' in reason) {
    const errorMessage = String(reason.message).toLowerCase();
    if (errorMessage.includes('flushing log events') || 
        errorMessage.includes('216.239.') || 
        errorMessage.includes('langsmith') ||
        errorMessage.includes('tracing')) {
      log.debug('LangChain telemetry error ignored:', reason.message);
      return; // Don't exit for telemetry errors
    }
  }
  
  log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  // Check if it's a network timeout error that we can ignore
  const nodeError = error as NodeJS.ErrnoException;
  if (nodeError.code === 'ETIMEDOUT' || nodeError.code === 'ECONNREFUSED') {
    log.debug('Network timeout ignored (likely telemetry):', error.message);
    return; // Don't exit for network timeouts
  }
  
  // Check for LangChain telemetry errors
  const errorMessage = error.message.toLowerCase();
  if (errorMessage.includes('flushing log events') || 
      errorMessage.includes('216.239.') || 
      errorMessage.includes('langsmith') ||
      errorMessage.includes('tracing')) {
    log.debug('LangChain telemetry error ignored:', error.message);
    return; // Don't exit for telemetry errors
  }
  
  log.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the CLI
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
