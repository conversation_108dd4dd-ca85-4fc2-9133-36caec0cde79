/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  ConfigParameters,
  AuthType,
  ApprovalMode,
  type MCPServerConfig,
  type TelemetrySettings,
  type AccessibilitySettings,
  type BugCommandSettings,
  type GeminiCLIExtension,
  type SummarizeToolOutputSettings,
  type FileFilteringOptions,
  FileDiscoveryService,
  GitService,
} from '@google/gemini-cli-core';

import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { Embeddings } from '@langchain/core/embeddings';
import { Tool } from '@langchain/core/tools';

import { LangChainContentGenerator } from '../core/contentGenerator.js';
import { LangChainToolRegistry } from '../tools/toolRegistry.js';
import { createChatModel, createEmbeddings, validateModelCompatibility } from '../core/modelFactory.js';
import { DEFAULT_LOCATION } from './constants.js';

import type { 
  LangChainConfig as LangChainConfigInterface, 
  LangChainContentGeneratorConfig
} from '../types/index.js';
import { ExtendedAuthType } from '../types/index.js';
import { HT_EMBEDDING_MODEL } from './models.js';
import { logger } from '../utils/logger.js';

// Extended ConfigParameters to support LangChain-specific properties
interface ExtendedConfigParameters extends ConfigParameters {
  baseURL?: string;
  authType?: ExtendedAuthType;
}

/**
 * LangChain-based configuration manager that mirrors the functionality
 * of the core Config class but uses LangChain components internally.
 */
export class LangChainConfig implements LangChainConfigInterface {
  private toolRegistry!: LangChainToolRegistry;
  private readonly sessionId: string;
  private contentGeneratorConfig!: LangChainContentGeneratorConfig;
  private readonly embeddingModel: string;
  private readonly targetDir: string;
  private readonly debugMode: boolean;
  private readonly question: string | undefined;
  private readonly fullContext: boolean;
  private readonly coreTools: string[] | undefined;
  private readonly excludeTools: string[] | undefined;
  private readonly toolDiscoveryCommand: string | undefined;
  private readonly toolCallCommand: string | undefined;
  private readonly mcpServerCommand: string | undefined;
  private readonly mcpServers: Record<string, MCPServerConfig> | undefined;
  private userMemory: string;
  private geminiMdFileCount: number;
  private approvalMode: ApprovalMode;
  private readonly showMemoryUsage: boolean;
  private readonly accessibility: AccessibilitySettings;
  private readonly telemetrySettings: TelemetrySettings;
  private readonly usageStatisticsEnabled: boolean;
  private langChainClient!: LangChainContentGenerator;
  private readonly fileFiltering: {
    respectGitIgnore: boolean;
    respectGeminiIgnore: boolean;
    enableRecursiveFileSearch: boolean;
  };
  private fileDiscoveryService: FileDiscoveryService | null = null;
  private gitService: GitService | undefined = undefined;
  private readonly checkpointing: boolean;
  private readonly proxy: string | undefined;
  private readonly cwd: string;
  private readonly bugCommand: BugCommandSettings | undefined;
  private model: string;
  private readonly extensionContextFilePaths: string[];
  private readonly noBrowser: boolean;
  private readonly ideMode: boolean;
  private modelSwitchedDuringSession: boolean = false;
  private readonly maxSessionTurns: number;
  private readonly listExtensions: boolean;
  private readonly _extensions: GeminiCLIExtension[];
  private readonly _blockedMcpServers: Array<{
    name: string;
    extensionName: string;
  }>;
  private quotaErrorOccurred: boolean = false;
  private readonly summarizeToolOutput:
    | Record<string, SummarizeToolOutputSettings>
    | undefined;
  private readonly experimentalAcp: boolean = false;

  // LangChain specific properties
  chatModel!: BaseChatModel;
  embeddings!: Embeddings;
  tools: Tool[] = [];
  temperature?: number;
  maxTokens?: number;
  streaming?: boolean;
  private readonly baseURL: string | undefined;
  private readonly authTypeOverride: ExtendedAuthType | undefined;

  constructor(params: ExtendedConfigParameters) {
    this.sessionId = params.sessionId;
    this.embeddingModel = params.embeddingModel || HT_EMBEDDING_MODEL; // 'text-embedding-004';
    this.targetDir = params.targetDir;
    this.debugMode = params.debugMode;
    this.question = params.question;
    this.fullContext = params.fullContext ?? false;
    this.coreTools = params.coreTools;
    this.excludeTools = params.excludeTools;
    this.toolDiscoveryCommand = params.toolDiscoveryCommand;
    this.toolCallCommand = params.toolCallCommand;
    this.mcpServerCommand = params.mcpServerCommand;
    this.mcpServers = params.mcpServers;
    this.userMemory = params.userMemory || '';
    this.geminiMdFileCount = params.geminiMdFileCount || 50;
    this.approvalMode = params.approvalMode || ApprovalMode.DEFAULT;
    this.showMemoryUsage = params.showMemoryUsage ?? false;
    this.accessibility = params.accessibility || {};
    this.telemetrySettings = params.telemetry || { enabled: false };
    this.usageStatisticsEnabled = params.usageStatisticsEnabled ?? false;
    this.fileFiltering = {
      respectGitIgnore: params.fileFiltering?.respectGitIgnore ?? true,
      respectGeminiIgnore: params.fileFiltering?.respectGeminiIgnore ?? true,
      enableRecursiveFileSearch: params.fileFiltering?.enableRecursiveFileSearch ?? true,
    };
    this.checkpointing = params.checkpointing ?? false;
    this.proxy = params.proxy;
    this.cwd = params.cwd;
    this.bugCommand = params.bugCommand;
    this.model = params.model;
    this.extensionContextFilePaths = params.extensionContextFilePaths || [];
    this.maxSessionTurns = params.maxSessionTurns || 100;
    this.listExtensions = params.listExtensions ?? false;
    this._extensions = params.extensions || [];
    this._blockedMcpServers = params.blockedMcpServers || [];
    this.noBrowser = params.noBrowser ?? false;
    this.summarizeToolOutput = params.summarizeToolOutput;
    this.ideMode = params.ideMode ?? false;
    this.baseURL = params.baseURL;
    this.authTypeOverride = params.authType;

    if (params.fileDiscoveryService) {
      this.fileDiscoveryService = params.fileDiscoveryService;
    }
  }

  async initialize(): Promise<void> {
    const authType = this.getAuthType();
    
    // Validate model compatibility
    const validation = validateModelCompatibility(this.model, authType);
    if (!validation.isValid && validation.suggestion) {
      logger.warning(`Model ${this.model} may not be compatible with auth type ${authType}. Consider using ${validation.suggestion}`);
    }
    
    // Initialize LangChain components
    this.chatModel = await createChatModel(
      this.model, 
      authType,
      process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY,
      process.env.GOOGLE_CLOUD_PROJECT,
      DEFAULT_LOCATION,
      this.baseURL
    );
    this.embeddings = await createEmbeddings(
      this.embeddingModel, 
      authType,
      process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY,
      process.env.GOOGLE_CLOUD_PROJECT,
      DEFAULT_LOCATION,
      this.baseURL
    );
    
    // Initialize tool registry
    this.toolRegistry = new LangChainToolRegistry(this);
    await this.toolRegistry.discoverTools();
    this.tools = this.toolRegistry.getLangChainTools();

    // Initialize content generator
    this.langChainClient = new LangChainContentGenerator(
      this.chatModel,
      this.embeddings,
      this.tools
    );
  }

  private getAuthType(): AuthType | ExtendedAuthType {
    // Use override if provided
    if (this.authTypeOverride) {
      return this.authTypeOverride;
    }
    
    // Determine auth type from environment or config
    if (process.env.ANTHROPIC_API_KEY) {
      return ExtendedAuthType.USE_ANTHROPIC;
    }
    if (process.env.OPENAI_API_KEY || process.env.OPENAI_BASE_URL) {
      return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
    }
    if (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true') {
      return AuthType.USE_VERTEX_AI;
    }
    if (process.env.GEMINI_API_KEY) {
      return AuthType.USE_GEMINI;
    }
    return AuthType.LOGIN_WITH_GOOGLE;
  }

  async refreshAuth(authMethod: AuthType | ExtendedAuthType): Promise<void> {
    // Recreate chat model with new auth
    this.chatModel = await createChatModel(
      this.model, 
      authMethod,
      process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY,
      process.env.GOOGLE_CLOUD_PROJECT,
      DEFAULT_LOCATION,
      this.baseURL
    );
    this.embeddings = await createEmbeddings(
      this.embeddingModel, 
      authMethod,
      process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY,
      process.env.GOOGLE_CLOUD_PROJECT,
      DEFAULT_LOCATION,
      this.baseURL
    );
    
    // Update content generator
    this.langChainClient = new LangChainContentGenerator(
      this.chatModel,
      this.embeddings,
      this.tools
    );
  }

  // Getters that mirror the core Config class
  getSessionId(): string {
    return this.sessionId;
  }

  getModel(): string {
    return this.model;
  }

  async setModel(newModel: string): Promise<void> {
    if (newModel === this.model) {
      return; // No change needed
    }

    // Validate the new model is compatible
    try {
      const authType = this.getAuthType();
      const validation = validateModelCompatibility(newModel, authType);
      if (!validation.isValid && validation.suggestion) {
        logger.warning(`Model ${newModel} may not be compatible with auth type ${authType}. Consider using ${validation.suggestion}`);
      }
      
      // Recreate chat model with new model name
      this.chatModel = await createChatModel(
        newModel,
        authType,
        process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY,
        process.env.GOOGLE_CLOUD_PROJECT,
        DEFAULT_LOCATION,
        this.baseURL
      );

      // Update content generator with new model
      this.langChainClient = new LangChainContentGenerator(
        this.chatModel,
        this.embeddings,
        this.tools
      );

      // Update the model property and mark as switched
      this.model = newModel;
      this.modelSwitchedDuringSession = true;
      
      logger.debug(`[LangChainConfig] Model switched to: ${newModel}`);
      
    } catch (error) {
      logger.error(`[LangChainConfig] Failed to switch model to ${newModel}:`, error);
      throw new Error(`Failed to switch model: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  isModelSwitchedDuringSession(): boolean {
    return this.modelSwitchedDuringSession;
  }

  getMaxSessionTurns(): number {
    return this.maxSessionTurns;
  }

  setQuotaErrorOccurred(value: boolean): void {
    this.quotaErrorOccurred = value;
  }

  getQuotaErrorOccurred(): boolean {
    return this.quotaErrorOccurred;
  }

  getEmbeddingModel(): string {
    return this.embeddingModel;
  }

  getTargetDir(): string {
    return this.targetDir;
  }

  getProjectRoot(): string {
    return this.targetDir;
  }

  async getToolRegistry(): Promise<LangChainToolRegistry> {
    return this.toolRegistry;
  }

  getDebugMode(): boolean {
    return this.debugMode;
  }

  getQuestion(): string | undefined {
    return this.question;
  }

  getFullContext(): boolean {
    return this.fullContext;
  }

  getCoreTools(): string[] | undefined {
    return this.coreTools;
  }

  getExcludeTools(): string[] | undefined {
    return this.excludeTools;
  }

  getToolDiscoveryCommand(): string | undefined {
    return this.toolDiscoveryCommand;
  }

  getToolCallCommand(): string | undefined {
    return this.toolCallCommand;
  }

  getMcpServerCommand(): string | undefined {
    return this.mcpServerCommand;
  }

  getMcpServers(): Record<string, MCPServerConfig> | undefined {
    return this.mcpServers;
  }

  getUserMemory(): string {
    return this.userMemory;
  }

  setUserMemory(newUserMemory: string): void {
    this.userMemory = newUserMemory;
  }

  getGeminiMdFileCount(): number {
    return this.geminiMdFileCount;
  }

  setGeminiMdFileCount(count: number): void {
    this.geminiMdFileCount = count;
  }

  getApprovalMode(): ApprovalMode {
    return this.approvalMode;
  }

  setApprovalMode(mode: ApprovalMode): void {
    this.approvalMode = mode;
  }

  getShowMemoryUsage(): boolean {
    return this.showMemoryUsage;
  }

  getAccessibility(): AccessibilitySettings {
    return this.accessibility;
  }

  getTelemetryEnabled(): boolean {
    return this.telemetrySettings.enabled ?? false;
  }

  getTelemetryLogPromptsEnabled(): boolean {
    return this.telemetrySettings.logPrompts ?? false;
  }

  getFileFilteringRespectGitIgnore(): boolean {
    return this.fileFiltering.respectGitIgnore;
  }

  getFileFilteringRespectGeminiIgnore(): boolean {
    return this.fileFiltering.respectGeminiIgnore;
  }

  getFileFilteringOptions(): FileFilteringOptions {
    return {
      respectGitIgnore: this.fileFiltering.respectGitIgnore,
      respectGeminiIgnore: this.fileFiltering.respectGeminiIgnore,
    };
  }

  getCheckpointingEnabled(): boolean {
    return this.checkpointing;
  }

  getProxy(): string | undefined {
    return this.proxy;
  }

  getWorkingDir(): string {
    return this.cwd;
  }

  getBugCommand(): BugCommandSettings | undefined {
    return this.bugCommand;
  }

  getUsageStatisticsEnabled(): boolean {
    return this.usageStatisticsEnabled;
  }

  getExtensionContextFilePaths(): string[] {
    return this.extensionContextFilePaths;
  }

  getExperimentalAcp(): boolean {
    return this.experimentalAcp;
  }

  getListExtensions(): boolean {
    return this.listExtensions;
  }

  getExtensions(): GeminiCLIExtension[] {
    return this._extensions;
  }

  getBlockedMcpServers(): Array<{ name: string; extensionName: string }> {
    return this._blockedMcpServers;
  }

  getNoBrowser(): boolean {
    return this.noBrowser;
  }

  isBrowserLaunchSuppressed(): boolean {
    return this.noBrowser;
  }

  getSummarizeToolOutputConfig():
    | Record<string, SummarizeToolOutputSettings>
    | undefined {
    return this.summarizeToolOutput;
  }

  getIdeMode(): boolean {
    return this.ideMode;
  }

  getFileService(): FileDiscoveryService {
    if (!this.fileDiscoveryService) {
      this.fileDiscoveryService = new FileDiscoveryService(this.getProjectRoot());
    }
    return this.fileDiscoveryService;
  }

  async getGitService(): Promise<GitService> {
    if (!this.gitService) {
      this.gitService = new GitService(this.getProjectRoot());
      await this.gitService.initialize();
    }
    return this.gitService;
  }

  // LangChain specific getters
  getChatModel(): BaseChatModel {
    return this.chatModel;
  }

  getEmbeddings(): Embeddings {
    return this.embeddings;
  }

  getTools(): Tool[] {
    return this.tools;
  }

  getLangChainClient(): LangChainContentGenerator {
    return this.langChainClient;
  }

  getLangChainConfig(): LangChainConfigInterface {
    return {
      chatModel: this.chatModel,
      embeddings: this.embeddings,
      tools: this.tools,
      temperature: 0.7,
      maxTokens: 4096,
      streaming: true,
    };
  }
}