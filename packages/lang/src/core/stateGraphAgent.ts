/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Tool } from '@langchain/core/tools';
import {
  AIMessage,
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from '@langchain/core/messages';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import logger from '../utils/logger.js';

// Import LangGraph components
import { StateGraph, Annotation } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { MemorySaver } from '@langchain/langgraph';
import type { BaseCheckpointSaver } from '@langchain/langgraph';

import type { LangChainConfig } from '../config/config.js';
import { LangChainLoopDetectionService } from './loopDetectionService.js';
import { LangChainTurnManager } from './turnManager.js';
import { LangChainPerformanceOptimizer } from './performanceOptimizer.js';

/**
 * State definition for the LangGraph agent
 */
const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (current: BaseMessage[], update: BaseMessage[]) => current.concat(update),
    default: () => [],
  }),
  userMemory: Annotation<string>({
    reducer: (current: string, update: string) => update || current,
    default: () => "",
  }),
  sessionId: Annotation<string>({
    reducer: (current: string, update: string) => update || current,
    default: () => "",
  }),
  systemPrompt: Annotation<string>({
    reducer: (current: string, update: string) => update || current,
    default: () => "",
  }),
  iterationCount: Annotation<number>({
    reducer: (current: number, update: number) => current + update,
    default: () => 0,
  }),
  maxIterations: Annotation<number>({
    reducer: (current: number, update: number) => update || current,
    default: () => 25,
  }),
});

type AgentStateType = typeof AgentState.State;

/**
 * LangGraph-based agent using StateGraph for better control and built-in features
 */
export class StateGraphAgent {
  private chatModel: BaseChatModel;
  private tools: Tool[];
  private systemPrompt: string;
  private config: LangChainConfig;
  private checkpointer?: BaseCheckpointSaver;
  private loopDetection: LangChainLoopDetectionService;
  private turnManager: LangChainTurnManager;
  private performanceOptimizer: LangChainPerformanceOptimizer;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private graph: any; // CompiledStateGraph instance

  constructor(
    config: LangChainConfig,
    systemPrompt?: string,
    options: {
      checkpointer?: BaseCheckpointSaver;
      enablePersistence?: boolean;
    } = {}
  ) {
    this.config = config;
    this.chatModel = config.chatModel;
    this.tools = config.tools;
    this.systemPrompt = systemPrompt || this.getDefaultSystemPrompt();
    
    // Set up checkpointer for persistence
    if (options.enablePersistence !== false) {
      this.checkpointer = options.checkpointer || new MemorySaver();
    }
    
    this.loopDetection = new LangChainLoopDetectionService(config);
    this.turnManager = new LangChainTurnManager({
      maxTurnsPerSession: 100,
      turnTimeoutMs: 300000, // 5 minutes
      enableTurnLogging: true,
      enableTurnCompression: true,
      compressionThreshold: 50,
    }, this.checkpointer);
    
    this.performanceOptimizer = new LangChainPerformanceOptimizer({
      maxCacheSize: 100, // 100MB
      maxCacheEntries: 1000,
      compressionRatio: 0.3, // 70% compression
      tokenLimit: 4000,
      cacheTTL: 3600000, // 1 hour
      enableCompression: true,
      enableCaching: true,
      compressionStrategy: 'summarization',
    }, this.chatModel);
    
    this.graph = this.buildGraph();
  }

  /**
   * Build the StateGraph with nodes and edges
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private buildGraph(): any {
    const workflow = new StateGraph(AgentState);

    // Add the agent node (calls the LLM)
    workflow.addNode("agent", this.callModel.bind(this));

    // Add tool node using LangGraph's built-in ToolNode
    const toolNode = new ToolNode(this.tools);
    workflow.addNode("tools", toolNode);

    // Set entry point - use the proper method for adding edges
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (workflow as any).addEdge("__start__", "agent");

    // Add conditional edges from agent
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (workflow as any).addConditionalEdges(
      "agent",
      this.shouldContinue.bind(this),
      {
        continue: "tools",
        end: "__end__",
      }
    );

    // Add edge from tools back to agent
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (workflow as any).addEdge("tools", "agent");

    // Compile the graph with checkpointer for persistence
    const compileOptions: { checkpointer?: BaseCheckpointSaver } = {};
    if (this.checkpointer) {
      compileOptions.checkpointer = this.checkpointer;
    }
    
    return workflow.compile(compileOptions);
  }

  /**
   * The agent node - calls the LLM with the current state
   */
  private async callModel(state: AgentStateType): Promise<Partial<AgentStateType>> {
    logger.debug('[StateGraphAgent] Calling model with', state.messages.length, 'messages');

    // Prepare messages with system prompt
    const messages = this.prepareMessages(state.messages, state.userMemory, state.systemPrompt);

    // Bind tools to the model if available
    const modelWithTools = this.tools.length > 0 && this.chatModel.bindTools
      ? this.chatModel.bindTools(this.tools)
      : this.chatModel;

    try {
      // Generate response
      const response = await modelWithTools.invoke(messages);
      
      // Increment iteration count
      const newIterationCount = state.iterationCount + 1;
      
      // Check if we've exceeded max iterations
      if (newIterationCount >= state.maxIterations) {
        logger.warning(`[StateGraphAgent] Maximum iterations (${state.maxIterations}) reached`);
        
        // Force end by creating a non-tool response
        const finalResponse = new AIMessage({
          content: response instanceof AIMessage && typeof response.content === 'string' 
            ? response.content 
            : "I've reached the maximum number of iterations. Let me know if you need further assistance.",
        });
        
        return {
          messages: [finalResponse],
          iterationCount: newIterationCount,
        };
      }

      return {
        messages: [response],
        iterationCount: newIterationCount,
      };
    } catch (error) {
      logger.error('[StateGraphAgent] Error calling model:', error);
      const errorMessage = new AIMessage({
        content: `Error: ${error instanceof Error ? error.message : String(error)}`,
      });
      
      return {
        messages: [errorMessage],
        iterationCount: state.iterationCount + 1,
      };
    }
  }

  /**
   * Determines whether to continue to tools or end
   */
  private shouldContinue(state: AgentStateType): string {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];
    
    // Check iteration limit
    if (state.iterationCount >= state.maxIterations) {
      logger.debug('[StateGraphAgent] Max iterations reached, ending');
      return "end";
    }

    // Check for loops using the detection service
    const loopDetection = this.loopDetection.detectLoop(messages);
    if (loopDetection.isLoop) {
      logger.warning(`[StateGraphAgent] Loop detected: ${loopDetection.reason} (confidence: ${loopDetection.confidence})`);
      return "end";
    }

    // If the last message has tool calls, continue to tools
    if (lastMessage instanceof AIMessage && lastMessage.tool_calls?.length) {
      logger.debug('[StateGraphAgent] Tool calls detected, continuing to tools');
      return "continue";
    }

    // Otherwise, end the conversation
    logger.debug('[StateGraphAgent] No tool calls, ending conversation');
    return "end";
  }

  /**
   * Prepare messages for the chat model, including system prompt
   */
  private prepareMessages(messages: BaseMessage[], userMemory?: string, systemPrompt?: string): BaseMessage[] {
    const preparedMessages: BaseMessage[] = [];
    
    // Add system prompt
    const prompt = systemPrompt || this.systemPrompt;
    if (prompt) {
      let finalPrompt = prompt;
      if (userMemory) {
        finalPrompt += `\n\nUser Memory:\n${userMemory}`;
      }
      preparedMessages.push(new SystemMessage(finalPrompt));
    }
    
    // Add conversation messages
    preparedMessages.push(...messages);
    
    return preparedMessages;
  }

  /**
   * Process a user message using the StateGraph
   */
  async processMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): Promise<string> {
    try {
      logger.debug('[StateGraphAgent] Processing message:', userMessage);
      
      // Validate input parameters
      if (!userMessage || typeof userMessage !== 'string') {
        throw new Error('Invalid user message: must be a non-empty string');
      }
      
      if (!sessionId || typeof sessionId !== 'string') {
        throw new Error('Invalid session ID: must be a non-empty string');
      }
      
      // Create initial state
      const initialState = {
        messages: [...conversationHistory, new HumanMessage(userMessage)],
        userMemory: userMemory || "",
        sessionId,
        systemPrompt: this.systemPrompt,
        iterationCount: 0,
        maxIterations: 25,
      };

      // Run the graph with recursion limit
      const result = await this.graph.invoke(initialState, {
        recursionLimit: 25,
      });
      
      // Extract the final response
      const finalMessages = result.messages;
      if (!finalMessages || finalMessages.length === 0) {
        logger.warning('[StateGraphAgent] No messages in result');
        return 'No response generated';
      }
      
      const lastMessage = finalMessages[finalMessages.length - 1];
      
      if (lastMessage instanceof AIMessage) {
        if (typeof lastMessage.content === 'string') {
          return lastMessage.content;
        } else if (lastMessage.content && typeof lastMessage.content === 'object') {
          return JSON.stringify(lastMessage.content);
        } else {
          return 'Empty response from model';
        }
      }
      
      logger.warning('[StateGraphAgent] Last message is not an AIMessage:', lastMessage?.constructor?.name);
      return 'No valid response generated';
    } catch (error) {
      logger.error('[StateGraphAgent] Error processing message:', error);
      
      // Provide more specific error messages based on error type
      if (error instanceof Error) {
        if (error.message.includes('recursion')) {
          throw new Error('Agent exceeded maximum recursion limit. Please try a simpler request.');
        } else if (error.message.includes('timeout')) {
          throw new Error('Request timed out. Please try again.');
        } else if (error.message.includes('quota')) {
          throw new Error('API quota exceeded. Please check your usage limits.');
        } else {
          throw new Error(`StateGraph agent processing failed: ${error.message}`);
        }
      } else {
        throw new Error(`StateGraph agent processing failed: ${String(error)}`);
      }
    }
  }

  /**
   * Stream responses using the StateGraph
   */
  async *streamMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    try {
      logger.debug('[StateGraphAgent] Streaming message:', userMessage);
      
      // Validate input parameters
      if (!userMessage || typeof userMessage !== 'string') {
        throw new Error('Invalid user message: must be a non-empty string');
      }
      
      if (!sessionId || typeof sessionId !== 'string') {
        throw new Error('Invalid session ID: must be a non-empty string');
      }
      
      // Create initial state
      const initialState = {
        messages: [...conversationHistory, new HumanMessage(userMessage)],
        userMemory: userMemory || "",
        sessionId,
        systemPrompt: this.systemPrompt,
        iterationCount: 0,
        maxIterations: 25,
      };

      // Stream the graph execution
      const stream = await this.graph.stream(initialState, {
        streamMode: "values",
        recursionLimit: 25,
      });
      
      let hasYielded = false;
      for await (const chunk of stream) {
        if (chunk.messages?.length) {
          const lastMessage = chunk.messages[chunk.messages.length - 1];
          if (lastMessage instanceof AIMessage && typeof lastMessage.content === 'string') {
            yield lastMessage.content;
            hasYielded = true;
          }
        }
      }
      
      // If no content was yielded, provide a fallback message
      if (!hasYielded) {
        yield 'No response content available';
      }
      
    } catch (error) {
      logger.error('[StateGraphAgent] Error streaming message:', error);
      
      // Provide more specific error messages based on error type
      if (error instanceof Error) {
        if (error.message.includes('recursion')) {
          yield 'Error: Agent exceeded maximum recursion limit. Please try a simpler request.';
        } else if (error.message.includes('timeout')) {
          yield 'Error: Request timed out. Please try again.';
        } else if (error.message.includes('quota')) {
          yield 'Error: API quota exceeded. Please check your usage limits.';
        } else {
          yield `Error: ${error.message}`;
        }
      } else {
        yield `Error: ${String(error)}`;
      }
    }
  }

  /**
   * Get the default system prompt
   */
  private getDefaultSystemPrompt(): string {
    return `You are an interactive CLI agent specializing in software engineering tasks. 
Your primary goal is to help users safely and efficiently.

You have access to various tools for file operations, web search, and code analysis.
Always use the appropriate tools to gather information before making changes.
Be concise and direct in your responses.`;
  }

  /**
   * Update the system prompt
   */
  updateSystemPrompt(prompt: string): void {
    this.systemPrompt = prompt;
    // Rebuild graph with new prompt
    this.graph = this.buildGraph();
  }

  /**
   * Update the model configuration
   */
  updateConfig(config: LangChainConfig): void {
    this.config = config;
    this.chatModel = config.chatModel;
    this.tools = config.tools;
    // Rebuild graph with new configuration
    this.graph = this.buildGraph();
  }

  /**
   * Get current configuration info
   */
  getConfigInfo(): {
    modelName: string;
    toolCount: number;
    hasSystemPrompt: boolean;
    maxIterations: number;
  } {
    return {
      modelName: this.chatModel.constructor.name,
      toolCount: this.tools.length,
      hasSystemPrompt: !!this.systemPrompt,
      maxIterations: 25,
    };
  }

  /**
   * Get the compiled graph for advanced operations
   */
  getGraph() {
    return this.graph;
  }
}