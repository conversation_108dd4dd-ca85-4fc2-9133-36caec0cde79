/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { Tool } from '@langchain/core/tools';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { z } from 'zod';

import { LangChainAgent } from './agent.js';
import { LangChainConfig } from '../config/config.js';

// Mock the chat model
vi.mock('@langchain/google-genai', () => ({
  ChatGoogleGenerativeAI: vi.fn().mockImplementation(() => ({
    invoke: vi.fn().mockResolvedValue({
      content: 'Mock AI response',
      additional_kwargs: {},
    }),
    stream: vi.fn().mockResolvedValue([
      { content: 'Mock', additional_kwargs: {} },
      { content: ' AI', additional_kwargs: {} },
      { content: ' response', additional_kwargs: {} },
    ]),
    bindTools: vi.fn().mockReturnThis(),
  })),
}));

// Mock tool with proper Zod schema
class _MockTool extends Tool {
  name = 'mock_tool';
  description = 'A mock tool for testing';
  schema = z.object({
    input: z.string().optional().describe('Input for the mock tool')
  }).transform((val) => val.input || '');

  async _call(input: string | undefined): Promise<string> {
    return `Mock tool result for: ${input || 'no input'}`;
  }
}

describe('LangChainAgent', () => {
  let mockConfig: LangChainConfig;
  let agent: LangChainAgent;
  let mockChatModel: BaseChatModel;

  beforeEach(async () => {
    mockChatModel = new ChatGoogleGenerativeAI();
    
    // Create a proper ExtendedConfigParameters object
    const configParams = {
      sessionId: 'test-session-123',
      targetDir: process.cwd(),
      debugMode: false,
      cwd: process.cwd(),
      model: 'gemini-1.5-flash',
      temperature: 0.7,
      maxTokens: 4096,
      streaming: true,
    };

    mockConfig = new LangChainConfig(configParams);
    await mockConfig.initialize();

    agent = new LangChainAgent(mockConfig);
  });

  describe('processMessage', () => {
    it('should process a simple message without tools', async () => {
      const response = await agent.processMessage(
        'Hello, how are you?',
        'test-session-1'
      );

      expect(response).toBeDefined();
      expect(typeof response).toBe('string');
      expect(mockChatModel.invoke).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Mock the chat model to throw an error
      vi.mocked(mockChatModel.invoke).mockRejectedValueOnce(
        new Error('Model error')
      );

      await expect(
        agent.processMessage('Test message', 'test-session-2')
      ).rejects.toThrow('Agent processing failed');
    });

    it('should include user memory in processing', async () => {
      const userMemory = 'User prefers TypeScript over JavaScript';
      
      await agent.processMessage(
        'What programming language should I use?',
        'test-session-3',
        userMemory
      );

      expect(mockChatModel.invoke).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            content: expect.stringContaining(userMemory),
          }),
        ])
      );
    });
  });

  describe('streamMessage', () => {
    it('should stream responses', async () => {
      const stream = agent.streamMessage(
        'Stream test message',
        'test-session-4'
      );

      const chunks: string[] = [];
      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      expect(chunks.length).toBeGreaterThan(0);
      expect(mockChatModel.stream).toHaveBeenCalled();
    });

    it('should handle streaming errors', async () => {
      vi.mocked(mockChatModel.stream).mockRejectedValueOnce(
        new Error('Streaming error')
      );

      const stream = agent.streamMessage(
        'Error test message',
        'test-session-5'
      );

      const chunks: string[] = [];
      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      expect(chunks).toContain('Error: Streaming error');
    });
  });

  describe('configuration', () => {
    it('should update system prompt', () => {
      const newPrompt = 'You are a helpful coding assistant.';
      agent.updateSystemPrompt(newPrompt);

      // We can't directly test the system prompt, but we can verify
      // the method doesn't throw an error
      expect(agent).toBeDefined();
    });

    it('should update configuration', async () => {
      // Create a new config with different temperature
      const newConfigParams = {
        sessionId: 'test-session-456',
        targetDir: process.cwd(),
        debugMode: false,
        cwd: process.cwd(),
        model: 'gemini-1.5-flash',
        temperature: 0.9,
        maxTokens: 4096,
        streaming: true,
      };
      
      const newConfig = new LangChainConfig(newConfigParams);
      await newConfig.initialize();

      agent.updateConfig(newConfig);

      const configInfo = agent.getConfigInfo();
      expect(configInfo.toolCount).toBeGreaterThanOrEqual(0);
    });

    it('should return configuration info', () => {
      const configInfo = agent.getConfigInfo();

      expect(configInfo).toHaveProperty('modelName');
      expect(configInfo).toHaveProperty('toolCount');
      expect(configInfo).toHaveProperty('hasSystemPrompt');
      expect(typeof configInfo.modelName).toBe('string');
      expect(typeof configInfo.toolCount).toBe('number');
      expect(typeof configInfo.hasSystemPrompt).toBe('boolean');
    });
  });

  describe('tool integration', () => {
    it('should handle tools in configuration', async () => {
      const toolConfigParams = {
        sessionId: 'test-session-tools',
        targetDir: process.cwd(),
        debugMode: false,
        cwd: process.cwd(),
        model: 'gemini-1.5-flash',
        temperature: 0.7,
        maxTokens: 4096,
        streaming: true,
      };
      
      const toolConfig = new LangChainConfig(toolConfigParams);
      await toolConfig.initialize();

      const agentWithTools = new LangChainAgent(toolConfig);
      const configInfo = agentWithTools.getConfigInfo();

      expect(configInfo.toolCount).toBeGreaterThanOrEqual(0);
    });

    it('should update tools when configuration changes', async () => {
      const newConfigParams = {
        sessionId: 'test-session-update-tools',
        targetDir: process.cwd(),
        debugMode: false,
        cwd: process.cwd(),
        model: 'gemini-1.5-flash',
        temperature: 0.7,
        maxTokens: 4096,
        streaming: true,
      };
      
      const newConfig = new LangChainConfig(newConfigParams);
      await newConfig.initialize();

      agent.updateConfig(newConfig);
      const configInfo = agent.getConfigInfo();

      expect(configInfo.toolCount).toBeGreaterThanOrEqual(0);
    });
  });
}); 