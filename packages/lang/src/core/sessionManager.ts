/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

import type { AgentState } from '../types/index.js';

/**
 * Session data interface
 */
export interface SessionData {
  id: string;
  createdAt: Date;
  lastActivity: Date;
  messages: BaseMessage[];
  userMemory?: string;
  metadata: Record<string, unknown>;
}

/**
 * Session manager that handles conversation persistence and memory
 */
export class SessionManager {
  private sessions: Map<string, SessionData> = new Map();
  private maxSessions: number;
  private sessionTimeout: number; // in milliseconds

  constructor(
    maxSessions = 100,
    sessionTimeout = 24 * 60 * 60 * 1000 // 24 hours
  ) {
    this.maxSessions = maxSessions;
    this.sessionTimeout = sessionTimeout;
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Create a new session
   */
  createSession(userMemory?: string, metadata?: Record<string, unknown>): string {
    const sessionId = uuidv4();
    const now = new Date();
    
    const session: SessionData = {
      id: sessionId,
      createdAt: now,
      lastActivity: now,
      messages: [],
      userMemory,
      metadata: metadata || {},
    };
    
    this.sessions.set(sessionId, session);
    
    // Clean up old sessions if we exceed the limit
    this.cleanupOldSessions();
    
    logger.debug(`[SessionManager] Created session: ${sessionId}`);
    return sessionId;
  }

  /**
   * Get an existing session
   */
  getSession(sessionId: string): SessionData | undefined {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivity = new Date();
    }
    return session;
  }

  /**
   * Update session with new messages
   */
  updateSession(
    sessionId: string, 
    newMessages: BaseMessage[],
    userMemory?: string
  ): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    session.messages.push(...newMessages);
    session.lastActivity = new Date();
    
    if (userMemory !== undefined) {
      session.userMemory = userMemory;
    }
    
    logger.debug(`[SessionManager] Updated session ${sessionId} with ${newMessages.length} messages`);
    return true;
  }

  /**
   * Add a single message to session
   */
  addMessage(sessionId: string, message: BaseMessage): boolean {
    return this.updateSession(sessionId, [message]);
  }

  /**
   * Get conversation history for a session
   */
  getConversationHistory(sessionId: string): BaseMessage[] {
    const session = this.sessions.get(sessionId);
    return session ? [...session.messages] : [];
  }

  /**
   * Get user memory for a session
   */
  getUserMemory(sessionId: string): string | undefined {
    const session = this.sessions.get(sessionId);
    return session?.userMemory;
  }

  /**
   * Update user memory for a session
   */
  updateUserMemory(sessionId: string, userMemory: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    session.userMemory = userMemory;
    session.lastActivity = new Date();
    
    logger.debug(`[SessionManager] Updated user memory for session ${sessionId}`);
    return true;
  }

  /**
   * Delete a session
   */
  deleteSession(sessionId: string): boolean {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      logger.debug(`[SessionManager] Deleted session: ${sessionId}`);
    }
    return deleted;
  }

  /**
   * Get all active session IDs
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * Get session count
   */
  getSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Get session metadata
   */
  getSessionMetadata(sessionId: string): Record<string, unknown> | undefined {
    const session = this.sessions.get(sessionId);
    return session?.metadata;
  }

  /**
   * Update session metadata
   */
  updateSessionMetadata(
    sessionId: string, 
    metadata: Record<string, unknown>
  ): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    session.metadata = { ...session.metadata, ...metadata };
    session.lastActivity = new Date();
    
    return true;
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): {
    messageCount: number;
    userMessageCount: number;
    aiMessageCount: number;
    sessionDuration: number;
    lastActivity: Date;
  } | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    const userMessageCount = session.messages.filter(m => m instanceof HumanMessage).length;
    const aiMessageCount = session.messages.filter(m => m instanceof AIMessage).length;
    const sessionDuration = session.lastActivity.getTime() - session.createdAt.getTime();
    
    return {
      messageCount: session.messages.length,
      userMessageCount,
      aiMessageCount,
      sessionDuration,
      lastActivity: session.lastActivity,
    };
  }

  /**
   * Convert session to AgentState
   */
  sessionToAgentState(sessionId: string): AgentState | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    return {
      messages: [...session.messages],
      sessionId: session.id,
      userMemory: session.userMemory,
      isComplete: false,
      toolResults: [],
    };
  }

  /**
   * Update session from AgentState
   */
  updateSessionFromAgentState(sessionId: string, state: AgentState): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    // Update messages (append new ones)
    const existingMessageCount = session.messages.length;
    const newMessages = state.messages.slice(existingMessageCount);
    
    if (newMessages.length > 0) {
      session.messages.push(...newMessages);
    }
    
    // Update user memory
    if (state.userMemory !== undefined) {
      session.userMemory = state.userMemory;
    }
    
    session.lastActivity = new Date();
    
    return true;
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity.getTime() > this.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }
    
    for (const sessionId of expiredSessions) {
      this.sessions.delete(sessionId);
      logger.debug(`[SessionManager] Expired session: ${sessionId}`);
    }
    
    if (expiredSessions.length > 0) {
      logger.debug(`[SessionManager] Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }

  /**
   * Clean up old sessions if we exceed the limit
   */
  private cleanupOldSessions(): void {
    if (this.sessions.size <= this.maxSessions) {
      return;
    }
    
    // Sort sessions by last activity (oldest first)
    const sortedSessions = Array.from(this.sessions.entries())
      .sort(([, a], [, b]) => a.lastActivity.getTime() - b.lastActivity.getTime());
    
    const toDelete = this.sessions.size - this.maxSessions;
    const deletedSessions: string[] = [];
    
    for (let i = 0; i < toDelete; i++) {
      const [sessionId] = sortedSessions[i];
      this.sessions.delete(sessionId);
      deletedSessions.push(sessionId);
    }
    
    logger.debug(`[SessionManager] Cleaned up ${deletedSessions.length} old sessions to maintain limit`);
  }

  /**
   * Start automatic cleanup interval
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000); // Run every hour
  }

  /**
   * Export session data (for persistence)
   */
  exportSession(sessionId: string): string | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return undefined;
    }
    
    // Convert messages to serializable format
    const serializedSession = {
      ...session,
      messages: session.messages.map(msg => ({
        type: msg.constructor.name,
        content: msg.content,
        additional_kwargs: msg.additional_kwargs,
      })),
    };
    
    return JSON.stringify(serializedSession, null, 2);
  }

  /**
   * Import session data (from persistence)
   */
  importSession(sessionData: string): string | undefined {
    try {
      const parsed = JSON.parse(sessionData);
      
      // Reconstruct messages
      const messages: BaseMessage[] = parsed.messages.map((msgData: Record<string, unknown>) => {
        switch (msgData.type) {
          case 'HumanMessage':
            return new HumanMessage(String(msgData.content));
          case 'AIMessage':
            return new AIMessage(String(msgData.content));
          default:
            return new HumanMessage(String(msgData.content)); // fallback
        }
      });
      
      const session: SessionData = {
        id: parsed.id,
        createdAt: new Date(parsed.createdAt),
        lastActivity: new Date(parsed.lastActivity),
        messages,
        userMemory: parsed.userMemory,
        metadata: parsed.metadata || {},
      };
      
      this.sessions.set(session.id, session);
      
      logger.debug(`[SessionManager] Imported session: ${session.id}`);
      return session.id;
    } catch (error) {
      logger.error('[SessionManager] Failed to import session:', error);
      return undefined;
    }
  }

  /**
   * Clear all sessions
   */
  clearAllSessions(): void {
    const count = this.sessions.size;
    this.sessions.clear();
    logger.debug(`[SessionManager] Cleared ${count} sessions`);
  }
}