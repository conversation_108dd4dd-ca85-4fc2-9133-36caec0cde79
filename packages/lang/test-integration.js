/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * 简单的集成测试
 * 验证Lang包的基本功能
 */

import { createLangChainGeminiCLI } from './dist/index.js';

async function testIntegration() {
  console.log('🧪 Lang包集成测试\n');

  try {
    // 测试CLI实例创建
    console.log('📝 测试1: 创建CLI实例');
    const cli = await createLangChainGeminiCLI({
      sessionId: 'test-session',
      model: 'gemini-1.5-flash',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: false,
    });

    console.log('✅ CLI实例创建成功');
    console.log(`📊 模型: ${cli.config.getModel()}`);
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    console.log(`📁 目标目录: ${cli.config.getTargetDir()}`);

    // 测试会话管理
    console.log('\n📝 测试2: 会话管理');
    const sessionId = cli.sessionManager.createSession('测试用户记忆');
    console.log(`✅ 会话创建成功: ${sessionId}`);
    
    const session = cli.sessionManager.getSession(sessionId);
    console.log(`✅ 会话获取成功: ${session ? '是' : '否'}`);

    // 测试工具发现
    console.log('\n📝 测试3: 工具发现');
    const toolRegistry = await cli.config.getToolRegistry();
    const tools = toolRegistry.getLangChainTools();
    console.log(`✅ 发现工具数量: ${tools.length}`);
    
    if (tools.length > 0) {
      console.log('📋 可用工具:');
      tools.slice(0, 3).forEach(tool => {
        console.log(`  - ${tool.name}: ${tool.description.substring(0, 50)}...`);
      });
    }

    // 测试配置功能
    console.log('\n📝 测试4: 配置功能');
    console.log(`✅ 调试模式: ${cli.config.getDebugMode()}`);
    console.log(`✅ 最大会话轮次: ${cli.config.getMaxSessionTurns()}`);
    console.log(`✅ 嵌入模型: ${cli.config.getEmbeddingModel()}`);

    console.log('\n🎉 所有测试通过！');
    console.log('\n📋 功能验证:');
    console.log('✅ CLI实例创建');
    console.log('✅ 会话管理');
    console.log('✅ 工具发现');
    console.log('✅ 配置管理');
    console.log('✅ 类型安全');
    console.log('✅ 编译通过');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testIntegration().catch(console.error); 