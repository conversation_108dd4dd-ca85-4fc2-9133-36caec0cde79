#!/usr/bin/env node
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Test script to verify tool calling functionality in the lang package
 */

import { createLangChainGeminiCLI } from './src/index.js';

async function testToolCalling() {
  console.log('🧪 测试工具调用功能...\n');

  try {
    // Test with LangChainAgent (default)
    console.log('📝 测试 LangChainAgent:');
    const cliAgent = await createLangChainGeminiCLI({
      sessionId: 'test-agent',
      model: 'ht::saas-deepseek-v3',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
      useStateGraph: false, // Use LangChainAgent
      coreTools: ['list_directory', 'read_file'], // Only enable basic tools for testing
    });

    console.log(`✅ LangChainAgent 创建成功`);
    console.log(`🔧 工具数量: ${cliAgent.config.getTools().length}`);
    console.log(`📋 可用工具: ${cliAgent.config.getTools().map(t => t.name).join(', ')}\n`);

    // Test tool calling
    console.log('🔍 测试工具调用 - 请求列出当前目录:');
    let response = '';
    for await (const chunk of cliAgent.streamMessage('请列出当前目录的内容')) {
      response += chunk;
      process.stdout.write(chunk);
    }
    console.log('\n');

    // Test with StateGraphAgent
    console.log('\n📝 测试 StateGraphAgent:');
    const cliStateGraph = await createLangChainGeminiCLI({
      sessionId: 'test-state-graph',
      model: 'ht::saas-deepseek-v3',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: true,
      useStateGraph: true, // Use StateGraphAgent
      coreTools: ['list_directory', 'read_file'], // Only enable basic tools for testing
    });

    console.log(`✅ StateGraphAgent 创建成功`);
    console.log(`🔧 工具数量: ${cliStateGraph.config.getTools().length}`);
    console.log(`📋 可用工具: ${cliStateGraph.config.getTools().map(t => t.name).join(', ')}\n`);

    // Test tool calling
    console.log('🔍 测试工具调用 - 请求读取package.json:');
    response = '';
    for await (const chunk of cliStateGraph.streamMessage('请读取package.json文件的内容')) {
      response += chunk;
      process.stdout.write(chunk);
    }
    console.log('\n');

    console.log('✅ 工具调用测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('堆栈:', error.stack);
    }
  }
}

// 检查是否有必要的环境变量
function checkEnvironment() {
  const hasOpenAI = process.env.OPENAI_API_KEY && process.env.OPENAI_BASE_URL;
  const hasAnthropic = process.env.ANTHROPIC_API_KEY;
  const hasGemini = process.env.GEMINI_API_KEY;

  if (!hasOpenAI && !hasAnthropic && !hasGemini) {
    console.log('⚠️  警告: 没有检测到API密钥环境变量');
    console.log('请设置以下环境变量之一:');
    console.log('- OPENAI_API_KEY 和 OPENAI_BASE_URL (OpenAI兼容)');
    console.log('- ANTHROPIC_API_KEY (Anthropic Claude)');
    console.log('- GEMINI_API_KEY (Google Gemini)');
    console.log('');
  }

  return hasOpenAI || hasAnthropic || hasGemini;
}

async function main() {
  console.log('🚀 Lang包工具调用功能测试\n');

  if (!checkEnvironment()) {
    console.log('❌ 缺少必要的环境变量，跳过测试');
    process.exit(1);
  }

  await testToolCalling();
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
