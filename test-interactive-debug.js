#!/usr/bin/env node

/**
 * Debug test for interactive mode
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testInteractiveDebug() {
  console.log('🧪 Testing interactive mode with detailed debugging...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  const child = spawn('node', [cliPath, '--interactive', '--debug'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let step = 0;
  let lastOutputTime = Date.now();
  let outputBuffer = '';
  const inputs = ['第一次输入测试', '第二次输入测试', 'exit'];

  child.stdout.on('data', (data) => {
    const text = data.toString();
    outputBuffer += text;
    lastOutputTime = Date.now();
    
    console.log(`[${new Date().toISOString()}] STDOUT:`, JSON.stringify(text));
    
    // Send next input when we see the prompt
    if (text.includes('> ') && step < inputs.length) {
      setTimeout(() => {
        console.log(`\n[${new Date().toISOString()}] 📝 Sending input ${step + 1}: ${inputs[step]}`);
        child.stdin.write(inputs[step] + '\n');
        step++;
      }, 1000);
    }
  });

  child.stderr.on('data', (data) => {
    const text = data.toString();
    console.log(`[${new Date().toISOString()}] STDERR:`, JSON.stringify(text));
  });

  // Monitor for hanging
  const hangMonitor = setInterval(() => {
    const timeSinceLastOutput = Date.now() - lastOutputTime;
    if (timeSinceLastOutput > 10000 && step > 0 && step < inputs.length) {
      console.log(`\n⚠️  No output for ${timeSinceLastOutput}ms, process may be hanging`);
      console.log(`Current step: ${step}, Last output buffer:`, JSON.stringify(outputBuffer.slice(-200)));
    }
  }, 5000);

  return new Promise((resolve, reject) => {
    child.on('close', (code) => {
      clearInterval(hangMonitor);
      console.log(`\n🏁 Process exited with code: ${code}`);
      resolve({ code, outputBuffer });
    });

    child.on('error', (error) => {
      clearInterval(hangMonitor);
      reject(error);
    });

    // Start the test
    setTimeout(() => {
      if (step === 0) {
        console.log('📝 Starting test...');
        child.stdin.write(inputs[step] + '\n');
        step++;
      }
    }, 3000);

    // Timeout after 60 seconds
    setTimeout(() => {
      clearInterval(hangMonitor);
      console.log('\n⏰ Test timeout reached');
      child.kill('SIGTERM');
      resolve({ code: -1, outputBuffer, timeout: true });
    }, 60000);
  });
}

async function main() {
  try {
    const result = await testInteractiveDebug();
    
    if (result.timeout) {
      console.log('\n⚠️  Test timed out - this suggests the second input is not being processed');
    } else {
      console.log('\n✅ Test completed');
    }
    
    // Analyze output
    const responses = result.outputBuffer.split('> ').length - 1;
    console.log(`\nAnalysis: Found ${responses} prompts in output`);
    
    if (result.outputBuffer.includes('第一次') && result.outputBuffer.includes('第二次')) {
      console.log('✅ Both inputs appear to have been processed');
    } else if (result.outputBuffer.includes('第一次')) {
      console.log('⚠️  Only first input was processed');
    } else {
      console.log('❌ No inputs were processed');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();