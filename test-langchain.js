#!/usr/bin/env node

/**
 * Test LangChain integration with custom model factory
 */

import { createChatModel } from './packages/lang/dist/core/modelFactory.js';
import { ExtendedAuthType } from './packages/lang/dist/types/index.js';
import { HumanMessage } from '@langchain/core/messages';

console.log('Testing LangChain with custom model factory...');

async function testModelFactory() {
  try {
    console.log('Creating model with factory...');
    
    const model = await createChatModel(
      'ht::saas-deepseek-r1',
      ExtendedAuthType.USE_OPENAI_COMPATIBLE,
      process.env.OPENAI_API_KEY || 'test-key',
      undefined,
      'us-central1',
      process.env.OPENAI_BASE_URL || 'http://168.63.85.222/web/unauth/LLM_api_proxy/v1'
    );
    
    console.log('✅ Model created successfully!');
    console.log('Model type:', model.constructor.name);
    
    // Test token counting (this was causing the original error)
    console.log('Testing token counting...');
    try {
      const tokenCount = await model.getNumTokens('What is 2+2?');
      console.log('✅ Token counting works! Count:', tokenCount);
    } catch (tokenError) {
      console.log('⚠️  Token counting failed (expected for custom models):', tokenError.message);
    }
    
    // Test actual model invocation
    console.log('Testing model invocation...');
    const messages = [new HumanMessage('What is 2+2?')];
    const response = await model.invoke(messages);
    
    console.log('✅ Model invocation successful!');
    console.log('Response type:', typeof response);
    console.log('Response content:', response.content);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.stack) {
      console.error('Stack:', error.stack);
    }
  }
}

// Also test direct ChatOpenAI usage for comparison
async function testDirectChatOpenAI() {
  console.log('\n--- Testing direct ChatOpenAI (for comparison) ---');
  
  try {
    const { ChatOpenAI } = await import('@langchain/openai');
    
    const model = new ChatOpenAI({
      modelName: 'ht::saas-deepseek-r1',
      apiKey: process.env.OPENAI_API_KEY || 'test-key',
      temperature: 0.7,
      maxTokens: 100,
      streaming: false,
      configuration: {
        baseURL: process.env.OPENAI_BASE_URL || 'http://168.63.85.222/web/unauth/LLM_api_proxy/v1',
      },
    });
    
    console.log('Testing direct token counting (this should fail)...');
    try {
      const tokenCount = await model.getNumTokens('What is 2+2?');
      console.log('Token count:', tokenCount);
    } catch (tokenError) {
      console.log('❌ Direct token counting failed as expected:', tokenError.message);
    }
    
    console.log('Testing direct model invocation...');
    const messages = [new HumanMessage('What is 2+2?')];
    const response = await model.invoke(messages);
    
    console.log('✅ Direct invocation successful!');
    console.log('Response content:', response.content);
    
  } catch (error) {
    console.error('❌ Direct test error:', error.message);
  }
}

async function runTests() {
  await testModelFactory();
  await testDirectChatOpenAI();
}

runTests();