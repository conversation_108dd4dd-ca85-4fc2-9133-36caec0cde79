#!/usr/bin/env node

/**
 * Simple test to verify the tiktoken fix
 */

import { createChatModel } from './packages/lang/dist/core/modelFactory.js';
import { ExtendedAuthType } from './packages/lang/dist/types/index.js';
import { HumanMessage } from '@langchain/core/messages';

console.log('🧪 Testing tiktoken fix for custom models...');

async function testTokenCountingFix() {
  try {
    console.log('1. Creating model with custom name: ht::saas-deepseek-r1');
    
    const model = await createChatModel(
      'ht::saas-deepseek-r1',
      ExtendedAuthType.USE_OPENAI_COMPATIBLE,
      'test-key',
      undefined,
      'us-central1',
      'http://localhost:8000/v1' // Dummy URL for testing
    );
    
    console.log('✅ Model created successfully!');
    
    // Test token counting - this was the source of the original error
    console.log('2. Testing token counting (this was failing before)...');
    
    try {
      const tokenCount = await model.getNumTokens('What is 2+2?');
      console.log('✅ getNumTokens works! Count:', tokenCount);
    } catch (error) {
      console.log('❌ getNumTokens failed:', error.message);
      return false;
    }
    
    try {
      const messages = [new HumanMessage('What is 2+2?')];
      const messageTokens = await model.getNumTokensFromMessages(messages);
      console.log('✅ getNumTokensFromMessages works! Result:', messageTokens);
    } catch (error) {
      console.log('❌ getNumTokensFromMessages failed:', error.message);
      return false;
    }
    
    console.log('3. Testing with different custom model names...');
    
    // Test with different custom model patterns
    const testModels = [
      'ht::saas-deepseek-v3',
      'custom-claude-model',
      'my-gemini-variant',
      'unknown-model-123'
    ];
    
    for (const modelName of testModels) {
      try {
        const testModel = await createChatModel(
          modelName,
          ExtendedAuthType.USE_OPENAI_COMPATIBLE,
          'test-key',
          undefined,
          'us-central1',
          'http://localhost:8000/v1'
        );
        
        const tokens = await testModel.getNumTokens('Hello world');
        console.log(`✅ ${modelName}: ${tokens} tokens`);
      } catch (error) {
        console.log(`❌ ${modelName} failed:`, error.message);
        return false;
      }
    }
    
    console.log('\n🎉 All tests passed! The tiktoken fix is working correctly.');
    console.log('\nSummary of the fix:');
    console.log('- Custom model names are now mapped to known tiktoken models for token counting');
    console.log('- DeepSeek models → gpt-4 tokenizer');
    console.log('- Claude models → gpt-4 tokenizer');
    console.log('- Gemini models → gpt-3.5-turbo tokenizer');
    console.log('- Unknown models → gpt-3.5-turbo tokenizer (fallback)');
    console.log('- Character-based estimation as ultimate fallback');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

testTokenCountingFix().then(success => {
  process.exit(success ? 0 : 1);
});