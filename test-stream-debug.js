#!/usr/bin/env node

/**
 * Debug test for streamMessage method
 */

import { createLangChainGeminiCLI } from './packages/lang/dist/index.js';
import { ExtendedAuthType } from './packages/lang/dist/types/index.js';

// Set telemetry environment variables
process.env.LANGCHAIN_TRACING_V2 = 'false';
process.env.LANGCHAIN_TRACING = 'false';
process.env.LANGCHAIN_ENDPOINT = '';
process.env.LANGCHAIN_API_KEY = '';
process.env.LANGSMITH_API_KEY = '';
process.env.LANGSMITH_TRACING = 'false';
process.env.OPENAI_ORGANIZATION = '';
process.env.LANGCHAIN_CALLBACKS_BACKGROUND = 'false';
process.env.LANGCHAIN_VERBOSE = 'false';
process.env.LANGCHAIN_DEBUG = 'false';
process.env.LANGCHAIN_LOG_LEVEL = 'ERROR';
process.env.LANGCHAIN_DISABLE_TELEMETRY = 'true';

// Override console.error to filter out telemetry errors
const originalConsoleError = console.error;
console.error = function(...args) {
  const message = args.join(' ');
  
  // Filter out telemetry-related errors
  if (message.includes('Error flushing log events') ||
      message.includes('216.239.') ||
      message.includes('ETIMEDOUT') ||
      message.includes('langsmith') ||
      message.includes('tracing')) {
    console.debug('Telemetry error suppressed:', message);
    return;
  }
  
  return originalConsoleError.apply(console, args);
};

function determineAuthType() {
  if (process.env.ANTHROPIC_API_KEY) {
    return ExtendedAuthType.USE_ANTHROPIC;
  }
  if (process.env.OPENAI_API_KEY || process.env.OPENAI_BASE_URL) {
    return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
  }
  if (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true' && process.env.GOOGLE_CLOUD_PROJECT) {
    return ExtendedAuthType.USE_VERTEX_AI;
  }
  if (process.env.GEMINI_API_KEY) {
    return ExtendedAuthType.USE_GEMINI;
  }

  return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
}

async function testStreamMessage() {
  console.log('🧪 Testing streamMessage directly...\n');
  
  try {
    // Create CLI instance
    const cli = await createLangChainGeminiCLI({
      sessionId: `test-${Date.now()}`,
      model: 'ht::saas-deepseek-r1',
      targetDir: process.cwd(),
      debugMode: true,
      question: '',
      fullContext: false,
      userMemory: '',
      geminiMdFileCount: 50,
      cwd: process.cwd(),
      authType: determineAuthType(),
    });

    console.log('✅ CLI instance created successfully');

    // Test first message
    console.log('\n📝 Testing first message...');
    const sessionId = `test-session-${Date.now()}`;
    
    try {
      const stream1 = cli.streamMessage('第一次输入测试', sessionId);
      let response1 = '';
      
      for await (const chunk of stream1) {
        response1 += chunk;
        process.stdout.write(chunk);
      }
      
      console.log('\n✅ First message completed, response length:', response1.length);
      
      // Test second message
      console.log('\n📝 Testing second message...');
      
      const stream2 = cli.streamMessage('第二次输入测试', sessionId);
      let response2 = '';
      
      for await (const chunk of stream2) {
        response2 += chunk;
        process.stdout.write(chunk);
      }
      
      console.log('\n✅ Second message completed, response length:', response2.length);
      
      if (response1.length > 0 && response2.length > 0) {
        console.log('\n🎉 Both messages processed successfully!');
      } else {
        console.log('\n❌ One or both messages failed to generate response');
      }
      
    } catch (streamError) {
      console.error('\n❌ Stream error:', streamError);
      throw streamError;
    }
    
  } catch (error) {
    console.error('\n💥 Test failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await testStreamMessage();
    process.exit(0);
  } catch (error) {
    console.error('Fatal error:', error.message);
    process.exit(1);
  }
}

main();