#!/usr/bin/env node

/**
 * Simple test to verify the network timeout fix
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testSingleCommand() {
  console.log('🧪 Testing hai-code with single command...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  // Test environment with telemetry disabled
  const env = {
    ...process.env,
    LANGCHAIN_TRACING_V2: 'false',
    LANGCHAIN_TRACING: 'false',
    LANGCHAIN_ENDPOINT: '',
    LANGCHAIN_API_KEY: '',
    LANGSMITH_API_KEY: '',
    LANGSMITH_TRACING: 'false',
    LANGCHAIN_CALLBACKS_BACKGROUND: 'false',
    LANGCHAIN_VERBOSE: 'false',
    LANGCHAIN_DEBUG: 'false',
    LANGCHAIN_LOG_LEVEL: 'ERROR',
    LANGCHAIN_DISABLE_TELEMETRY: 'true',
    HAI_CODE_MODEL: 'ht::saas-deepseek-r1'
  };

  return new Promise((resolve, reject) => {
    const child = spawn('node', [cliPath, '简单测试'], {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';
    let hasNetworkError = false;

    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
    });

    child.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      
      // Check for unhandled network timeout errors
      if (text.includes('ETIMEDOUT') && !text.includes('ignored')) {
        console.error('❌ Unhandled network timeout error detected:', text);
        hasNetworkError = true;
      }
      
      // Show handled errors
      if (text.includes('Network timeout ignored') || 
          text.includes('LangChain telemetry error ignored') ||
          text.includes('Blocked telemetry request')) {
        console.log('✅ Network error properly handled:', text.trim());
      } else {
        process.stderr.write(text);
      }
    });

    child.on('close', (code) => {
      console.log(`\n🏁 Process exited with code: ${code}`);
      
      if (hasNetworkError) {
        reject(new Error('Unhandled network timeout detected'));
      } else if (code === 0) {
        console.log('✅ Test passed: No unhandled network timeouts');
        resolve({ success: true, output, errorOutput });
      } else {
        console.log('⚠️  Process exited with non-zero code, but no network errors detected');
        resolve({ success: true, output, errorOutput, code });
      }
    });

    child.on('error', (error) => {
      console.error('❌ Process error:', error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('Test timeout after 30 seconds'));
    }, 30000);
  });
}

async function main() {
  try {
    await testSingleCommand();
    console.log('\n🎉 Network timeout fix test completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();