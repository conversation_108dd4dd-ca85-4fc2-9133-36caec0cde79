#!/usr/bin/env node

/**
 * Test script to verify the network timeout fix for hai-code
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testInteractiveMode() {
  console.log('🧪 Testing hai-code network timeout fix...\n');
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  // Test environment with telemetry disabled
  const env = {
    ...process.env,
    LANGCHAIN_TRACING_V2: 'false',
    LANGCHAIN_TRACING: 'false',
    LANGCHAIN_ENDPOINT: '',
    LANGCHAIN_API_KEY: '',
    LANGSMITH_API_KEY: '',
    LANGSMITH_TRACING: 'false',
    LANGCHAIN_CALLBACKS_BACKGROUND: 'false',
    LANGCHAIN_VERBOSE: 'false',
    LANGCHAIN_DEBUG: 'false',
    LANGCHAIN_LOG_LEVEL: 'ERROR',
    LANGCHAIN_DISABLE_TELEMETRY: 'true',
    // Use a test model
    HAI_CODE_MODEL: 'ht::saas-deepseek-r1'
  };

  return new Promise((resolve, reject) => {
    const child = spawn('node', [cliPath, '--interactive'], {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';
    let step = 0;
    
    const testSteps = [
      '介绍这个仓库',
      '分析packages/cli文件',
      'exit'
    ];

    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
      
      // Wait for prompt and send next input
      if (text.includes('> ') && step < testSteps.length) {
        setTimeout(() => {
          console.log(`\n📝 Sending: ${testSteps[step]}`);
          child.stdin.write(testSteps[step] + '\n');
          step++;
        }, 1000);
      }
    });

    child.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      
      // Check for network timeout errors
      if (text.includes('ETIMEDOUT') && !text.includes('ignored')) {
        console.error('❌ Network timeout error detected:', text);
        reject(new Error('Network timeout not properly handled'));
        return;
      }
      
      // Show debug output but don't fail on telemetry errors
      if (text.includes('Network timeout ignored') || 
          text.includes('LangChain telemetry error ignored')) {
        console.log('✅ Telemetry error properly ignored:', text.trim());
      } else {
        process.stderr.write(text);
      }
    });

    child.on('close', (code) => {
      console.log(`\n🏁 Process exited with code: ${code}`);
      
      if (code === 0) {
        console.log('✅ Test passed: No unhandled network timeouts');
        resolve({ success: true, output, errorOutput });
      } else {
        console.log('❌ Test failed: Process exited with non-zero code');
        reject(new Error(`Process exited with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error('❌ Process error:', error);
      reject(error);
    });

    // Start the test
    setTimeout(() => {
      if (step === 0) {
        console.log('📝 Starting interactive test...');
        child.stdin.write(testSteps[step] + '\n');
        step++;
      }
    }, 2000);

    // Timeout after 60 seconds
    setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('Test timeout after 60 seconds'));
    }, 60000);
  });
}

async function main() {
  try {
    await testInteractiveMode();
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

main();