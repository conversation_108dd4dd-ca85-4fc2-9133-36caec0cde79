#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Test script to verify the interactive mode fixes
 */

const { spawn } = require('child_process');
const path = require('path');

async function testInteractiveMode() {
  console.log('🧪 Testing interactive mode fixes...\n');
  
  // Set environment variables to prevent network issues
  const env = {
    ...process.env,
    LANGCHAIN_TRACING_V2: 'false',
    LANGCHAIN_ENDPOINT: '',
    LANGCHAIN_API_KEY: '',
    LANGSMITH_API_KEY: '',
    OPENAI_ORGANIZATION: '',
    // Use a local test endpoint to avoid real API calls
    OPENAI_BASE_URL: 'http://localhost:11434/v1',
    OPENAI_API_KEY: 'test-key'
  };
  
  const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
  
  console.log('Starting CLI in interactive mode...');
  const child = spawn('node', [cliPath, '--interactive'], {
    env,
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let output = '';
  let errorOutput = '';
  
  child.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    process.stdout.write(text);
  });
  
  child.stderr.on('data', (data) => {
    const text = data.toString();
    errorOutput += text;
    
    // Filter out expected network timeout errors
    if (!text.includes('ETIMEDOUT') && !text.includes('216.239')) {
      process.stderr.write(text);
    }
  });
  
  // Wait a moment for initialization
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test first question
  console.log('\n📝 Testing first question...');
  child.stdin.write('介绍这个仓库\n');
  
  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test second question
  console.log('\n📝 Testing second question...');
  child.stdin.write('core目录\n');
  
  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test third question to verify session continuity
  console.log('\n📝 Testing third question (session continuity)...');
  child.stdin.write('刚才我问了什么？\n');
  
  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Exit
  child.stdin.write('exit\n');
  
  child.on('close', (code) => {
    console.log(`\n✅ CLI exited with code: ${code}`);
    
    // Check for issues
    const hasThinkingIssue = output.includes('</think>') && !output.includes('💭');
    const hasNetworkError = errorOutput.includes('ETIMEDOUT') && errorOutput.includes('216.239');
    const hasSessionContinuity = output.includes('interactive-') || output.includes('Created interactive session');
    
    console.log('\n📊 Test Results:');
    console.log(`- Thinking tags handled properly: ${!hasThinkingIssue ? '✅' : '❌'}`);
    console.log(`- Network errors suppressed: ${!hasNetworkError ? '✅' : '❌'}`);
    console.log(`- Session continuity working: ${hasSessionContinuity ? '✅' : '❌'}`);
    
    if (!hasThinkingIssue && !hasNetworkError && hasSessionContinuity) {
      console.log('\n🎉 All fixes appear to be working!');
    } else {
      console.log('\n⚠️  Some issues may still exist.');
      if (hasThinkingIssue) console.log('   - Thinking tags still have issues');
      if (hasNetworkError) console.log('   - Network errors not properly suppressed');
      if (!hasSessionContinuity) console.log('   - Session continuity not working');
    }
  });
}

// Run the test
testInteractiveMode().catch(console.error);